<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection 1.0//EN" "http://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
<node>
    <interface name="org.kde.StatusNotifierWatcher">
        <method name="RegisterStatusNotifierHost">
            <arg name="serviceName" type="s" direction="in"></arg>
        </method>
        <method name="RegisterStatusNotifierItem">
            <arg name="serviceOrPath" type="s" direction="in"></arg>
        </method>
        <signal name="StatusNotifierItemRegistered">
            <arg name="ServiceName" type="s"></arg>
        </signal>
        <signal name="StatusNotifierItemUnregistered">
            <arg name="ServiceName" type="s"></arg>
        </signal>
        <signal name="StatusNotifierHostRegistered"></signal>
        <property name="RegisteredStatusNotifierItems" type="as" access="read"></property>
        <property name="IsStatusNotifierHostRegistered" type="b" access="read"></property>
        <property name="ProtocolVersion" type="i" access="read"></property>
    </interface>
</node>