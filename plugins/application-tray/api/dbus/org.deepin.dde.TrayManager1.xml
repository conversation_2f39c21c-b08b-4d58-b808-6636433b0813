<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection 1.0//EN" "http://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
<node>
    <interface name="org.deepin.dde.TrayManager1">
        <method name="EnableNotification">
            <arg name="win" type="u" direction="in"></arg>
            <arg name="enabled" type="b" direction="in"></arg>
        </method>
        <method name="GetName">
            <arg name="win" type="u" direction="in"></arg>
            <arg name="name" type="s" direction="out"></arg>
        </method>
        <method name="Manage">
            <arg name="ok" type="b" direction="out"></arg>
        </method>
        <signal name="Inited"></signal>
        <signal name="Added">
            <arg name="id" type="u"></arg>
        </signal>
        <signal name="Removed">
            <arg name="id" type="u"></arg>
        </signal>
        <signal name="Changed">
            <arg name="id" type="u"></arg>
        </signal>
        <property name="TrayIcons" type="au" access="read">
            <annotation name="org.qtproject.QtDBus.QtTypeName" value="TrayList"/>
        </property>
    </interface>
</node>