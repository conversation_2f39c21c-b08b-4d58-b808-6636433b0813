<?xml version="1.0" ?><!DOCTYPE TS><TS version="2.1" language="te">
<context>
    <name>MonitorItemWidget</name>
    <message>
        <location filename="../src/widget/monitoritemdelegate.cpp" line="347"/>
        <source>Connecting</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/widget/monitoritemdelegate.cpp" line="354"/>
        <source>Cancel</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::StatePanel</name>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="425"/>
        <source>Disconnect</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="408"/>
        <location filename="../src/wirelesscastingapplet.cpp" line="489"/>
        <source>No available casting wireless monitors found</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="463"/>
        <source>Successfully cast the screen to %1</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="485"/>
        <source>The wireless card doesn&apos;t support this feature, and cannot cast the screen to a wireless monitor</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="487"/>
        <source>You need to enable wireless network to cast the screen to a wireless monitor</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::WirelessCastingApplet</name>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="77"/>
        <source>Wireless Casting</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="110"/>
        <source>Multiple Display options</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="156"/>
        <source>Display settings</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="209"/>
        <source>Duplicate</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="210"/>
        <source>Extend</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="214"/>
        <source>Only on %1</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::WirelessCastingItem</name>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="45"/>
        <location filename="../src/wirelesscastingitem.cpp" line="93"/>
        <source>Multiple services started</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="50"/>
        <location filename="../src/wirelesscastingitem.cpp" line="98"/>
        <source>Connecting</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="52"/>
        <location filename="../src/wirelesscastingitem.cpp" line="100"/>
        <location filename="../src/wirelesscastingitem.cpp" line="173"/>
        <source>Not connected</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="172"/>
        <source>Multiple Displays</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="200"/>
        <source>Display settings</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::WirelessCastingPlugin</name>
    <message>
        <location filename="../src/wirelesscastingplugin.cpp" line="149"/>
        <source>Multiple Displays</source>
        <translation type="unfinished"/>
    </message>
</context>
</TS>