<?xml version="1.0" ?><!DOCTYPE TS><TS version="2.1" language="zh_CN">
<context>
    <name>MonitorItemWidget</name>
    <message>
        <location filename="../src/widget/monitoritemdelegate.cpp" line="347"/>
        <source>Connecting</source>
        <translation>正在连接</translation>
    </message>
    <message>
        <location filename="../src/widget/monitoritemdelegate.cpp" line="354"/>
        <source>Cancel</source>
        <translation>取消连接</translation>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::StatePanel</name>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="425"/>
        <source>Disconnect</source>
        <translation>断开连接</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="408"/>
        <location filename="../src/wirelesscastingapplet.cpp" line="489"/>
        <source>No available casting wireless monitors found</source>
        <translation>没有找到可用的无线显示器</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="463"/>
        <source>Successfully cast the screen to %1</source>
        <translation>已成功投屏至%1</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="485"/>
        <source>The wireless card doesn&apos;t support this feature, and cannot cast the screen to a wireless monitor</source>
        <translation>无线网卡不支持此功能，无法将屏幕投送至无线显示器</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="487"/>
        <source>You need to enable wireless network to cast the screen to a wireless monitor</source>
        <translation>需要启用无线网卡，才能将屏幕投送至无线显示器</translation>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::WirelessCastingApplet</name>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="77"/>
        <source>Wireless Casting</source>
        <translation>无线投屏</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="110"/>
        <source>Multiple Display options</source>
        <translation>多屏显示选项</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="156"/>
        <source>Display settings</source>
        <translation>显示设置</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="209"/>
        <source>Duplicate</source>
        <translation>复制</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="210"/>
        <source>Extend</source>
        <translation>扩展</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="214"/>
        <source>Only on %1</source>
        <translation>仅%1屏</translation>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::WirelessCastingItem</name>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="45"/>
        <location filename="../src/wirelesscastingitem.cpp" line="93"/>
        <source>Multiple services started</source>
        <translation>多个服务开启</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="50"/>
        <location filename="../src/wirelesscastingitem.cpp" line="98"/>
        <source>Connecting</source>
        <translation>正在连接</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="52"/>
        <location filename="../src/wirelesscastingitem.cpp" line="100"/>
        <location filename="../src/wirelesscastingitem.cpp" line="173"/>
        <source>Not connected</source>
        <translation>未连接</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="172"/>
        <source>Multiple Displays</source>
        <translation>多屏显示</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="200"/>
        <source>Display settings</source>
        <translation>显示设置</translation>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::WirelessCastingPlugin</name>
    <message>
        <location filename="../src/wirelesscastingplugin.cpp" line="149"/>
        <source>Multiple Displays</source>
        <translation>多屏显示</translation>
    </message>
</context>
</TS>