<?xml version="1.0" ?><!DOCTYPE TS><TS version="2.1" language="zh_HK">
<context>
    <name>MonitorItemWidget</name>
    <message>
        <location filename="../src/widget/monitoritemdelegate.cpp" line="347"/>
        <source>Connecting</source>
        <translation>正在連接</translation>
    </message>
    <message>
        <location filename="../src/widget/monitoritemdelegate.cpp" line="354"/>
        <source>Cancel</source>
        <translation>取消連接</translation>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::StatePanel</name>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="425"/>
        <source>Disconnect</source>
        <translation>斷開連接</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="408"/>
        <location filename="../src/wirelesscastingapplet.cpp" line="489"/>
        <source>No available casting wireless monitors found</source>
        <translation>沒有找到可用的無線顯示器</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="463"/>
        <source>Successfully cast the screen to %1</source>
        <translation>已成功投屏至%1</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="485"/>
        <source>The wireless card doesn&apos;t support this feature, and cannot cast the screen to a wireless monitor</source>
        <translation>無線網卡不支持此功能，無法將螢幕投送至無線顯示器</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="487"/>
        <source>You need to enable wireless network to cast the screen to a wireless monitor</source>
        <translation>需要啟用無線網卡，才能將螢幕投送至無線顯示器</translation>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::WirelessCastingApplet</name>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="77"/>
        <source>Wireless Casting</source>
        <translation>無線投屏</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="110"/>
        <source>Multiple Display options</source>
        <translation>多屏顯示選項</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="156"/>
        <source>Display settings</source>
        <translation>顯示設置</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="209"/>
        <source>Duplicate</source>
        <translation>複製</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="210"/>
        <source>Extend</source>
        <translation>擴展</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingapplet.cpp" line="214"/>
        <source>Only on %1</source>
        <translation>僅%1屏</translation>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::WirelessCastingItem</name>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="45"/>
        <location filename="../src/wirelesscastingitem.cpp" line="93"/>
        <source>Multiple services started</source>
        <translation>多個服務開啟</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="50"/>
        <location filename="../src/wirelesscastingitem.cpp" line="98"/>
        <source>Connecting</source>
        <translation>連接中</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="52"/>
        <location filename="../src/wirelesscastingitem.cpp" line="100"/>
        <location filename="../src/wirelesscastingitem.cpp" line="173"/>
        <source>Not connected</source>
        <translation>未連接</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="172"/>
        <source>Multiple Displays</source>
        <translation>多屏顯示</translation>
    </message>
    <message>
        <location filename="../src/wirelesscastingitem.cpp" line="200"/>
        <source>Display settings</source>
        <translation>顯示設置</translation>
    </message>
</context>
<context>
    <name>dde::wirelesscasting::WirelessCastingPlugin</name>
    <message>
        <location filename="../src/wirelesscastingplugin.cpp" line="149"/>
        <source>Multiple Displays</source>
        <translation>多屏顯示</translation>
    </message>
</context>
</TS>