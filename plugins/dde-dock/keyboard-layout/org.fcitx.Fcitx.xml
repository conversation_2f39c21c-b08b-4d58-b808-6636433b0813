<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection 1.0//EN"
    "http://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
    <node>
        <interface name="org.fcitx.Fcitx.InputMethod">
            <method name="CreateIC">
                <arg name="icid" direction="out" type="i"/>
                <arg name="keyval1" direction="out" type="u"/>
                <arg name="state1" direction="out" type="u"/>
                <arg name="keyval2" direction="out" type="u"/>
                <arg name="state2" direction="out" type="u"/>
            </method>
            <method name="CreateICv2">
                <arg name="appname" direction="in" type="s"/>
                <arg name="icid" direction="out" type="i"/>
                <arg name="enable" direction="out" type="b"/>
                <arg name="keyval1" direction="out" type="u"/>
                <arg name="state1" direction="out" type="u"/>
                <arg name="keyval2" direction="out" type="u"/>
                <arg name="state2" direction="out" type="u"/>
            </method>
            <method name="CreateICv3">
                <arg name="appname" direction="in" type="s"/>
                <arg name="pid" direction="in" type="i"/>
                <arg name="icid" direction="out" type="i"/>
                <arg name="enable" direction="out" type="b"/>
                <arg name="keyval1" direction="out" type="u"/>
                <arg name="state1" direction="out" type="u"/>
                <arg name="keyval2" direction="out" type="u"/>
                <arg name="state2" direction="out" type="u"/>
            </method>
            <method name="Exit"></method>
            <method name="GetCurrentIM">
                <arg name="im" direction="out" type="s"/>
            </method>
            <method name="GetIMByIndex">
                <arg name="index" direction="in" type="i"/>
                <arg name="im" direction="out" type="s"/>
            </method>
            <method name="SetCurrentIM">
                <arg name="im" direction="in" type="s"/>
            </method>
            <method name="ReloadConfig"></method>
            <method name="ReloadAddonConfig">
                <arg name="addon" direction="in" type="s"/>
            </method>
            <method name="Restart"></method>
            <method name="Configure"></method>
            <method name="ConfigureAddon">
                <arg name="addon" direction="in" type="s"/>
            </method>
            <method name="ConfigureIM">
                <arg name="im" direction="in" type="s"/>
            </method>
            <method name="GetCurrentUI">
                <arg name="addon" direction="out" type="s"/>
            </method>
            <method name="GetIMAddon">
                <arg name="im" direction="in" type="s"/>
                <arg name="addon" direction="out" type="s"/>
            </method>
            <method name="ActivateIM"></method>
            <method name="InactivateIM"></method>
            <method name="ToggleIM"></method>
            <method name="SwitchIM"></method>
            <method name="ResetIMList"></method>
            <method name="GetCurrentState">
                <arg name="state" direction="out" type="i"/>
            </method>
            <signal name="ReloadConfigUI"></signal>
            <property access="readwrite" type="a(sssb)" name="IMList">
                <annotation name="org.qtproject.QtDBus.QtTypeName" value="FcitxQtInputMethodItemList"/>
                <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="true"/>
            </property>
            <property access="readwrite" type="s" name="CurrentIM">
                <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="true"/>
            </property>
        </interface>
    </node>
