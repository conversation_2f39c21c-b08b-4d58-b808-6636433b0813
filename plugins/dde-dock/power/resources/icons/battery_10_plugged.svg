<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.7 (28169) - http://www.bohemiancoding.com/sketch -->
    <title>battery_100_plugged</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="21" cy="21" r="21"></circle>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="battery_100_plugged">
            <g transform="translate(3.000000, 3.000000)">
                <g id="circle15750">
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use fill="#9AD82D" fill-rule="evenodd" xlink:href="#path-1"></use>
                </g>
                <circle id="circle15756" fill="#FFFFFF" cx="21" cy="21" r="12"></circle>
                <path d="M21.8589,12.041631 L16,22.989431 L21.0355,22.989431 L20.014,30.912731 C20.004,30.994731 20.112,31.029731 20.1487,30.953731 L26.0156,20.016031 L20.9984,20.030031 L21.9936,12.082331 C22.0036,12.000331 21.8946,11.967031 21.8589,12.041331 L21.8589,12.041631 Z" id="path15758" fill="#9AD82D"></path>
            </g>
        </g>
    </g>
</svg>