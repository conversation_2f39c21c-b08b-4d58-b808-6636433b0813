<interface name="org.deepin.dde.AirplaneMode1">
    <method name="DumpState" />
    <method name="Enable">
        <arg name="enabled" type="b" direction="in"></arg>
    </method>
    <method name="EnableBluetooth">
        <arg name="enabled" type="b" direction="in"></arg>
    </method>
    <method name="EnableWifi">
        <arg name="enabled" type="b" direction="in"></arg>
    </method>

    <property name="BluetoothEnabled" type="b" access="read"></property>
    <property name="Enabled" type="b" access="read"></property>
    <property name="WifiEnabled" type="b" access="read"></property>
</interface>
