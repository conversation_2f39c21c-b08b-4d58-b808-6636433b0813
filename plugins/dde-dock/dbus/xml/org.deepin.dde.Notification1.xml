<interface name="org.deepin.dde.Notification1">
  <method name="CloseNotification">
    <arg direction="in" type="u"/>
  </method>
  <method name="GetCapbilities">
    <arg direction="out" type="as"/>
  </method>
  <method name="GetServerInformation">
    <arg direction="out" type="s"/>
    <arg direction="out" type="s"/>
    <arg direction="out" type="s"/>
    <arg direction="out" type="s"/>
  </method>
  <method name="Notify">
    <arg direction="in" type="s"/>
    <arg direction="in" type="u"/>
    <arg direction="in" type="s"/>
    <arg direction="in" type="s"/>
    <arg direction="in" type="s"/>
    <arg direction="in" type="as"/>
    <arg direction="in" type="a{sv}"/>
    <annotation name="org.qtproject.QtDBus.QtTypeName.In6" value="QVariantMap"/>
    <arg direction="in" type="i"/>
    <arg direction="out" type="u"/>
  </method>
  <method name="GetAllRecords"> 
    <arg direction="out" type="s"/>
  </method> 
  <method name="GetRecordById"> 
    <arg direction="in" type="s"/>
    <arg direction="out" type="s"/>
  </method> 
  <method name="GetRecordsFromId"> 
    <arg direction="in" type="i"/>
    <arg direction="in" type="s"/>
    <arg direction="out" type="s"/>
  </method> 
  <method name="RemoveRecord"> 
    <arg direction="in" type="s"/>
  </method> 
  <method name="ClearRecords"/>
  <method name="getAppSetting"> 
    <arg direction="in" type="s"/>
    <arg direction="out" type="s"/>
  </method>
  <method name="Toggle"/>
  <method name="Show"/>
  <method name="Hide"/>
  <method name="recordCount">
    <arg direction="out" type="u"/>
  </method>
  <method name="GetAppList">
    <arg direction="out" type="as"/>
  </method>
  <method name="GetAppInfo">
    <arg direction="in" type="s"/>
    <arg direction="in" type="u"/>
    <arg direction="out" type="v"/>
  </method>
  <method name="GetSystemInfo">
    <arg direction="in" type="u"/>
    <arg direction="out" type="v"/>
  </method>
  <method name="SetAppInfo">
    <arg direction="in" type="s"/>
    <arg direction="in" type="u"/>
    <arg direction="in" type="v"/>
  </method>
  <method name="SetSystemInfo">
    <arg direction="in" type="u"/>
    <arg direction="in" type="v"/>
  </method>
  <method name="setAppSetting">
    <arg direction="in" type="s"/>
  </method>
  <signal name="NotificationClosed">
    <arg type="u"/>
    <arg type="u"/>
  </signal>
  <signal name="ActionInvoked">
    <arg type="u"/>
    <arg type="s"/>
  </signal>
  <signal name="RecordAdded"> 
    <arg type="s"/>
  </signal>
  <signal name="AppInfoChanged"> 
    <arg type="s"/>
    <arg type="u"/>
    <arg type="v"/>
  </signal>
  <signal name="SystemInfoChanged"> 
    <arg type="u"/>
    <arg type="v"/>
  </signal>
  <signal name="AppAddedSignal"> 
    <arg type="s"/>
  </signal>
  <signal name="AppRemovedSignal"> 
    <arg type="s"/>
  </signal>
  <signal name="appRemoved"> 
    <arg type="s"/>
  </signal>
  <signal name="appAdded"> 
    <arg type="s"/>
  </signal>
  <signal name="appSettingChanged"> 
    <arg type="s"/>
  </signal>
  <signal name="systemSettingChanged"> 
    <arg type="s"/>
  </signal>
  <property name="allSetting" type="s" access="readwrite"/>
  <property name="systemSetting" type="s" access="readwrite"/>
</interface>
