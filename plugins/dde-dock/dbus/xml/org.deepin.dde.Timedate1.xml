<interface name="org.deepin.dde.Timedate1">
     <signal name="TimeUpdate">
     </signal>
     <method name="AddUserTimezone">
          <arg type="s" direction="in"></arg>
     </method>
     <method name="DeleteUserTimezone">
          <arg type="s" direction="in"></arg>
     </method>
     <method name="GetZoneInfo">
          <arg type="s" direction="in"></arg>
          <arg type="(ssi(xxi))" direction="out"></arg>
          <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value ="ZoneInfo"/>
     </method>
     <method name="GetZoneList">
          <arg type="as" direction="out"></arg>
     </method>
     <method name="GetSampleNTPServers">
          <arg type="as" direction="out"></arg>
     </method>
     <method name="SetDate">
          <arg type="i" direction="in"></arg>
          <arg type="i" direction="in"></arg>
          <arg type="i" direction="in"></arg>
          <arg type="i" direction="in"></arg>
          <arg type="i" direction="in"></arg>
          <arg type="i" direction="in"></arg>
          <arg type="i" direction="in"></arg>
     </method>
     <method name="SetLocalRTC">
          <arg type="b" direction="in"></arg>
          <arg type="b" direction="in"></arg>
     </method>
     <method name="SetNTP">
          <arg type="b" direction="in"></arg>
     </method>
     <method name="SetNTPServer">
          <arg type="s" direction="in"></arg>
     </method>
     <method name="SetTime">
          <arg type="x" direction="in"></arg>
          <arg type="b" direction="in"></arg>
     </method>
     <method name="SetTimezone">
          <arg type="s" direction="in"></arg>
     </method>
     <property name="CanNTP" type="b" access="read"></property>
     <property name="NTP" type="b" access="read"></property>
     <property name="LocalRTC" type="b" access="read"></property>
     <property name="Timezone" type="s" access="read"></property>
     <property name="Use24HourFormat" type="b" access="readwrite"></property>
     <property name="DSTOffset" type="i" access="readwrite"></property>
     <property name="UserTimezones" type="as" access="read"></property>
     <property name="NTPServer" type="s" access="read"></property>
     <property name="WeekdayFormat" type="i" access="readwrite"></property>
     <property name="ShortDateFormat" type="i" access="readwrite"></property>
     <property name="LongDateFormat" type="i" access="readwrite"></property>
     <property name="ShortTimeFormat" type="i" access="readwrite"></property>
     <property name="LongTimeFormat" type="i" access="readwrite"></property>
     <property name="WeekBegins" type="i" access="readwrite"></property>
</interface>
