
     <interface name="org.deepin.dde.Network1">
          <method name="ActivateAccessPoint">
               <arg type="s" direction="in"></arg>
               <arg type="o" direction="in"></arg>
               <arg type="o" direction="in"></arg>
               <arg type="o" direction="out"></arg>
          </method>
          <method name="ActivateConnection">
               <arg type="s" direction="in"></arg>
               <arg type="o" direction="in"></arg>
               <arg type="o" direction="out"></arg>
          </method>
          <method name="CancelSecret">
               <arg type="s" direction="in"></arg>
               <arg type="s" direction="in"></arg>
          </method>
          <method name="CreateConnection">
               <arg type="s" direction="in"></arg>
               <arg type="o" direction="in"></arg>
               <arg type="o" direction="out"></arg>
          </method>
          <method name="CreateConnectionForAccessPoint">
               <arg type="o" direction="in"></arg>
               <arg type="o" direction="in"></arg>
               <arg type="o" direction="out"></arg>
          </method>
          <method name="DeactivateConnection">
               <arg type="s" direction="in"></arg>
          </method>
          <method name="DeleteConnection">
               <arg type="s" direction="in"></arg>
          </method>
          <method name="DisableWirelessHotspotMode">
               <arg type="o" direction="in"></arg>
          </method>
          <method name="DisconnectDevice">
               <arg type="o" direction="in"></arg>
          </method>
          <method name="EditConnection">
               <arg type="s" direction="in"></arg>
               <arg type="o" direction="in"></arg>
               <arg type="o" direction="out"></arg>
          </method>
          <method name="EnableDevice">
               <arg type="o" direction="in"></arg>
               <arg type="b" direction="in"></arg>
          </method>
          <method name="EnableWirelessHotspotMode">
               <arg type="o" direction="in"></arg>
          </method>
          <method name="ListDeviceConnections">
               <arg type="ao" direction="out"></arg>
               <arg type="o" direction="in"></arg>
          </method>
          <method name="FeedSecret">
               <arg type="s" direction="in"></arg>
               <arg type="s" direction="in"></arg>
               <arg type="s" direction="in"></arg>
               <arg type="b" direction="in"></arg>
          </method>
          <method name="GetAccessPoints">
               <arg type="o" direction="in"></arg>
               <arg type="s" direction="out"></arg>
          </method>
          <method name="GetActiveConnectionInfo">
               <arg type="s" direction="out"></arg>
          </method>
          <method name="GetAutoProxy">
               <arg type="s" direction="out"></arg>
          </method>
          <method name="GetProxy">
               <arg type="s" direction="in"></arg>
               <arg type="s" direction="out"></arg>
               <arg type="s" direction="out"></arg>
          </method>
          <method name="GetProxyIgnoreHosts">
               <arg type="s" direction="out"></arg>
          </method>
          <method name="GetProxyMethod">
               <arg type="s" direction="out"></arg>
          </method>
          <method name="GetSupportedConnectionTypes">
               <arg type="as" direction="out"></arg>
          </method>
          <method name="GetWiredConnectionUuid">
               <arg type="o" direction="in"></arg>
               <arg type="s" direction="out"></arg>
          </method>
          <method name="IsDeviceEnabled">
               <arg type="o" direction="in"></arg>
               <arg type="b" direction="out"></arg>
          </method>
          <method name="IsPasswordValid">
               <arg type="s" direction="in"></arg>
               <arg type="s" direction="in"></arg>
               <arg type="b" direction="out"></arg>
          </method>
          <method name="IsWirelessHotspotModeEnabled">
               <arg type="o" direction="in"></arg>
               <arg type="b" direction="out"></arg>
          </method>
          <method name="RegisterSecretReceiver"></method>
          <method name="RequestWirelessScan"></method>
          <method name="SetAutoProxy">
               <arg type="s" direction="in"></arg>
          </method>
          <method name="SetDeviceManaged">
               <arg type="s" direction="in"></arg>
               <arg type="b" direction="in"></arg>
          </method>
          <method name="SetProxy">
               <arg type="s" direction="in"></arg>
               <arg type="s" direction="in"></arg>
               <arg type="s" direction="in"></arg>
          </method>
          <method name="SetProxyIgnoreHosts">
               <arg type="s" direction="in"></arg>
          </method>
          <method name="SetProxyMethod">
               <arg type="s" direction="in"></arg>
          </method>
          <method name="RequestIPConflictCheck">
               <arg type="s" direction="in"></arg>
               <arg type="s" direction="in"></arg>
          </method>
          <signal name="NeedSecrets">
               <arg type="s"></arg>
          </signal>
          <signal name="NeedSecretsFinished">
               <arg type="s"></arg>
               <arg type="s"></arg>
          </signal>
          <signal name="AccessPointAdded">
               <arg type="s"></arg>
               <arg type="s"></arg>
          </signal>
          <signal name="AccessPointRemoved">
               <arg type="s"></arg>
               <arg type="s"></arg>
          </signal>
          <signal name="AccessPointPropertiesChanged">
               <arg type="s"></arg>
               <arg type="s"></arg>
          </signal>
          <signal name="DeviceEnabled">
               <arg type="s"></arg>
               <arg type="b"></arg>
          </signal>
          <signal name="ActiveConnectionInfoChanged"/>
          <signal name="IPConflict">
               <arg type="s"></arg>
               <arg type="s"></arg>
          </signal>
          <property name="State" type="u" access="read"></property>
          <property name="WirelessAccessPoints" type="s" access="read"></property>
          <property name="Connectivity" type="u" access="read"></property>
          <property name="NetworkingEnabled" type="b" access="readwrite"></property>
          <property name="VpnEnabled" type="b" access="readwrite"></property>
          <property name="Devices" type="s" access="read"></property>
          <property name="Connections" type="s" access="read"></property>
          <property name="ActiveConnections" type="s" access="read"></property>
     </interface>
