// SPDX-FileCopyrightText: 2024 UnionTech Software Technology Co., Ltd.
//
// SPDX-License-Identifier: LGPL-3.0-or-later

#pragma once

#include <QLoggingCategory>

Q_DECLARE_LOGGING_CATEGORY(APP)
Q_DECLARE_LOGGING_CATEGORY(DOCK_DATETIME)
Q_DECLARE_LOGGING_CATEGORY(SHUTDOWN)
Q_DECLARE_LOGGING_CATEGORY(DOCK_POWER)
Q_DECLARE_LOGGING_CATEGORY(DOCK_SOUND)
Q_DECLARE_LOGGING_CATEGORY(TRAY)
Q_DECLARE_LOGGING_CATEGORY(TRASH)
Q_DECLARE_LOGGING_CATEGORY(KEYBOARD_LAYOUT)
Q_DECLARE_LOGGING_CATEGORY(OVERLAY_WARNING)
Q_DECLARE_LOGGING_CATEGORY(SHOW_DESKTOP)
Q_DECLARE_LOGGING_CATEGORY(MULTI_TASK)
Q_DECLARE_LOGGING_CATEGORY(BLUETOOTH)
Q_DECLARE_LOGGING_CATEGORY(AIRPLANE)
Q_DECLARE_LOGGING_CATEGORY(QUICK_PANEL)
Q_DECLARE_LOGGING_CATEGORY(DND)
Q_DECLARE_LOGGING_CATEGORY(EYE_COMFORT)
Q_DECLARE_LOGGING_CATEGORY(MEDIA)
Q_DECLARE_LOGGING_CATEGORY(BRIGHTNESS)
Q_DECLARE_LOGGING_CATEGORY(PERFORMANCE)

Q_DECLARE_LOGGING_CATEGORY(DCC_DOCK_SETTING)

