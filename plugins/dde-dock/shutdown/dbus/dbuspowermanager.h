// SPDX-FileCopyrightText: 2024 UnionTech Software Technology Co., Ltd.
//
// SPDX-License-Identifier: LGPL-3.0-or-later

/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp -c PowerManager -p com_deepin_daemon_powermanager org.deepin.dde.PowerManager1.xml
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#ifndef COM_DEEPIN_DAEMON_POWERMANAGER_H
#define COM_DEEPIN_DAEMON_POWERMANAGER_H

#include <QtCore/QObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>
#include <QtDBus/QtDBus>

/*
 * Proxy class for interface org.deepin.dde.PowerManager1
 */
class DBusPowerManager: public QDBusAbstractInterface
{
    Q_OBJECT
public:
    static inline const char *staticInterfaceName()
    { return "org.deepin.dde.PowerManager1"; }

public:
    DBusPowerManager(const QString &service, const QString &path, const QDBusConnection &connection, QObject *parent = nullptr);

    ~DBusPowerManager();

public Q_SLOTS: // METHODS
    inline QDBusPendingReply<bool> CanHibernate()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("CanHibernate"), argumentList);
    }

    inline QDBusPendingReply<bool> CanReboot()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("CanReboot"), argumentList);
    }

    inline QDBusPendingReply<bool> CanShutdown()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("CanShutdown"), argumentList);
    }

    inline QDBusPendingReply<bool> CanSuspend()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("CanSuspend"), argumentList);
    }

Q_SIGNALS: // SIGNALS
};

namespace org {
  namespace deepin {
    namespace dde {
      typedef ::DBusPowerManager PowerManager;
    }
  }
}
#endif
