{"magic": "dsg.config.meta", "version": "1.0", "contents": {"dockedTrayPlugins": {"value": ["sni:Fcitx", "mount-item-key", "sni:uos-activator"], "serial": 0, "flags": [], "name": "dockedTrayPlugins", "name[zh_CN]": "驻留在任务栏上的托盘插件", "description[zh_CN]": "驻留在任务栏上的托盘插件，内容是插件的名称，仅处理驻留状态，不涉及插件的展示顺序。\n此配置和 gsettings 中的plugin-settings 配置项同时起作用，两个配置有一个是驻留那么插件即驻留。\n重启后生效", "description": "", "permissions": "readwrite", "visibility": "private"}}}