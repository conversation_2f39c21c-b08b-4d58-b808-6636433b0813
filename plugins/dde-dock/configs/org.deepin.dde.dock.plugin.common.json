{"magic": "dsg.config.meta", "version": "1.0", "contents": {"defaultDockedPlugins": {"value": ["multitasking", "show-desktop", "battery", "shutdown", "datetime"], "serial": 0, "flags": [], "name": "default docked plugins", "name[zh_CN]": "默认驻留在任务栏上的插件", "description[zh_CN]": "默认驻留在任务栏上的插件，内容是插件的名称，仅处理默认驻留的状态，代码不会也不应该修改此值。\n注意：快捷面板里面的插件要默认驻留在任务栏，需要修改org.deepin.dde.dock.plugin.quick-panel中的dockedQuickPlugins配置。  \n重启后生效", "description": "", "permissions": "readonly", "visibility": "public"}, "pluginsOrder": {"value": "{\"2\": [\"dde-tray\", \"network-item-key\", \"dde-quick-panel\", \"AiAssistant\", \"notifications\", \"power\", \"shutdown\"], \"3\": [ \"show-desktop\", \"multitasking\" ], \"7\": [ \"datetime\", \"trash\" ] }", "serial": 0, "flags": [], "name": "Plguins order", "name[zh_CN]": "任务栏插件顺序", "description[zh_CN]": "任务栏在运行时的插件顺序，跟随任务栏插件位置变动。2-普通插件区域，3-固定区域（最左侧），7-工具插件区域（最右边）", "description": "", "permissions": "readwrite", "visibility": "private"}}}