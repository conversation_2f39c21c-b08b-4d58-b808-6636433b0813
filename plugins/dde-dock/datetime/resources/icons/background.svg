<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.8.3 (29802) - http://www.bohemiancoding.com/sketch -->
    <title>时钟底板</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="21" cy="21" r="21"></circle>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="时钟底板">
            <g id="Group-3" transform="translate(3.000000, 3.000000)">
                <g id="Oval-2">
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use fill="#F9AF15" fill-rule="evenodd" xlink:href="#path-1"></use>
                </g>
                <circle id="Oval-2" fill="#FFFFFF" cx="21" cy="21" r="18.5"></circle>
            </g>
        </g>
    </g>
</svg>