/**
 * This file is generated by dconfig2cpp.
 * Command line arguments: ./dconfig2cpp -p ./dde-tray-loader/toolGenerate/dconfig2cpp ./dde-tray-loader/plugins/dde-dock/configs/org.deepin.dde.dock.plugin.common.json
 * Generation time: 2025-01-14T10:55:03
 * JSON file version: 1.0
 * 
 * WARNING: DO NOT MODIFY THIS FILE MANUALLY.
 * If you need to change the content, please modify the dconfig2cpp tool.
 */

#ifndef ORG_DEEPIN_DDE_DOCK_PLUGIN_COMMON_H
#define ORG_DEEPIN_DDE_DOCK_PLUGIN_COMMON_H

#include <QThread>
#include <QVariant>
#include <QDebug>
#include <QAtomicPointer>
#include <QAtomicInteger>
#include <DConfig>

class org_deepin_dde_dock_plugin_common : public QObject {
    Q_OBJECT

    Q_PROPERTY(QList<QVariant> defaultDockedPlugins READ defaultDockedPlugins WRITE setDefaultDockedPlugins NOTIFY defaultDockedPluginsChanged)
    Q_PROPERTY(QString pluginsOrder READ pluginsOrder WRITE setPluginsOrder NOTIFY pluginsOrderChanged)
public:
    explicit org_deepin_dde_dock_plugin_common(QThread *thread, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_dock_plugin_common(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_dock_plugin_common(QThread *thread, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_dock_plugin_common(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    ~org_deepin_dde_dock_plugin_common() {
        if (m_config.loadRelaxed()) {
            m_config.loadRelaxed()->deleteLater();
        }
    }

    QList<QVariant> defaultDockedPlugins() const {
        return p_defaultDockedPlugins;
    }
    void setDefaultDockedPlugins(const QList<QVariant> &value) {
        auto oldValue = p_defaultDockedPlugins;
        p_defaultDockedPlugins = value;
        markPropertySet(0);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("defaultDockedPlugins"), value);
            });
        }
        if (p_defaultDockedPlugins != oldValue) {
            Q_EMIT defaultDockedPluginsChanged();
        }
    }
    QString pluginsOrder() const {
        return p_pluginsOrder;
    }
    void setPluginsOrder(const QString &value) {
        auto oldValue = p_pluginsOrder;
        p_pluginsOrder = value;
        markPropertySet(1);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("pluginsOrder"), value);
            });
        }
        if (p_pluginsOrder != oldValue) {
            Q_EMIT pluginsOrderChanged();
        }
    }
Q_SIGNALS:
    void defaultDockedPluginsChanged();
    void pluginsOrderChanged();
private:
    void initialize(DTK_CORE_NAMESPACE::DConfig *config) {
        Q_ASSERT(!m_config.loadRelaxed());
        m_config.storeRelaxed(config);
        if (testPropertySet(0)) {
            config->setValue(QStringLiteral("defaultDockedPlugins"), QVariant::fromValue(p_defaultDockedPlugins));
        } else {
            updateValue(QStringLiteral("defaultDockedPlugins"), QVariant::fromValue(p_defaultDockedPlugins));
        }
        if (testPropertySet(1)) {
            config->setValue(QStringLiteral("pluginsOrder"), QVariant::fromValue(p_pluginsOrder));
        } else {
            updateValue(QStringLiteral("pluginsOrder"), QVariant::fromValue(p_pluginsOrder));
        }

        connect(config, &DTK_CORE_NAMESPACE::DConfig::valueChanged, this, [this](const QString &key) {
            updateValue(key);
        }, Qt::DirectConnection);
    }
    void updateValue(const QString &key, const QVariant &fallback = QVariant()) {
        Q_ASSERT(QThread::currentThread() == m_config.loadRelaxed()->thread());
        const QVariant &value = m_config.loadRelaxed()->value(key, fallback);
        if (key == QStringLiteral("defaultDockedPlugins")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_defaultDockedPlugins != newValue) {
                    p_defaultDockedPlugins = newValue;
                    Q_EMIT defaultDockedPluginsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("pluginsOrder")) {
            auto newValue = qvariant_cast<QString>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_pluginsOrder != newValue) {
                    p_pluginsOrder = newValue;
                    Q_EMIT pluginsOrderChanged();
                }
            });
            return;
        }
    }
    inline void markPropertySet(const int index) {
        if (index < 32) {
            m_propertySetStatus0.fetchAndOrOrdered(1 << (index - 0));
            return;
        }
        Q_UNREACHABLE();
    }
    inline bool testPropertySet(const int index) const {
        if (index < 32) {
            return (m_propertySetStatus0.loadRelaxed() & (1 << (index - 0)));
        }
        Q_UNREACHABLE();
    }
    QAtomicPointer<DTK_CORE_NAMESPACE::DConfig> m_config = nullptr;
    QList<QVariant> p_defaultDockedPlugins { QList<QVariant>{QVariant(QStringLiteral("multitasking")), QVariant(QStringLiteral("show-desktop")), QVariant(QStringLiteral("battery")), QVariant(QStringLiteral("shutdown")), QVariant(QStringLiteral("datetime"))} };
    QString p_pluginsOrder { QStringLiteral("{"2": ["dde-tray", "network-item-key", "dde-quick-panel", "AiAssistant", "notifications", "power", "shutdown"], "3": [ "show-desktop", "multitasking" ], "7": [ "datetime", "trash" ] }") };
    QAtomicInteger<quint32> m_propertySetStatus0 = 0;
};

#endif // ORG_DEEPIN_DDE_DOCK_PLUGIN_COMMON_H
