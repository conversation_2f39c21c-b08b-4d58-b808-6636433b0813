/**
 * This file is generated by dconfig2cpp.
 * Command line arguments: ./dconfig2cpp -p ./dde-tray-loader/toolGenerate/dconfig2cpp ./dde-tray-loader/plugins/dde-dock/configs/org.deepin.dde.dock.plugin.sound.json
 * Generation time: 2025-01-14T10:55:03
 * JSON file version: 1.0
 * 
 * WARNING: DO NOT MODIFY THIS FILE MANUALLY.
 * If you need to change the content, please modify the dconfig2cpp tool.
 */

#ifndef ORG_DEEPIN_DDE_DOCK_PLUGIN_SOUND_H
#define ORG_DEEPIN_DDE_DOCK_PLUGIN_SOUND_H

#include <QThread>
#include <QVariant>
#include <QDebug>
#include <QAtomicPointer>
#include <QAtomicInteger>
#include <DConfig>

class org_deepin_dde_dock_plugin_sound : public QObject {
    Q_OBJECT

    Q_PROPERTY(bool enableAdjustVolumeNoCard READ enableAdjustVolumeNoCard WRITE setEnableAdjustVolumeNoCard NOTIFY enableAdjustVolumeNoCardChanged)
    Q_PROPERTY(double soundOutputSlider READ soundOutputSlider WRITE setSoundOutputSlider NOTIFY soundOutputSliderChanged)
public:
    explicit org_deepin_dde_dock_plugin_sound(QThread *thread, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_dock_plugin_sound(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_dock_plugin_sound(QThread *thread, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_dock_plugin_sound(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    ~org_deepin_dde_dock_plugin_sound() {
        if (m_config.loadRelaxed()) {
            m_config.loadRelaxed()->deleteLater();
        }
    }

    bool enableAdjustVolumeNoCard() const {
        return p_enableAdjustVolumeNoCard;
    }
    void setEnableAdjustVolumeNoCard(const bool &value) {
        auto oldValue = p_enableAdjustVolumeNoCard;
        p_enableAdjustVolumeNoCard = value;
        markPropertySet(0);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("enableAdjustVolumeNoCard"), value);
            });
        }
        if (p_enableAdjustVolumeNoCard != oldValue) {
            Q_EMIT enableAdjustVolumeNoCardChanged();
        }
    }
    double soundOutputSlider() const {
        return p_soundOutputSlider;
    }
    void setSoundOutputSlider(const double &value) {
        auto oldValue = p_soundOutputSlider;
        p_soundOutputSlider = value;
        markPropertySet(1);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("soundOutputSlider"), value);
            });
        }
        if (p_soundOutputSlider != oldValue) {
            Q_EMIT soundOutputSliderChanged();
        }
    }
Q_SIGNALS:
    void enableAdjustVolumeNoCardChanged();
    void soundOutputSliderChanged();
private:
    void initialize(DTK_CORE_NAMESPACE::DConfig *config) {
        Q_ASSERT(!m_config.loadRelaxed());
        m_config.storeRelaxed(config);
        if (testPropertySet(0)) {
            config->setValue(QStringLiteral("enableAdjustVolumeNoCard"), QVariant::fromValue(p_enableAdjustVolumeNoCard));
        } else {
            updateValue(QStringLiteral("enableAdjustVolumeNoCard"), QVariant::fromValue(p_enableAdjustVolumeNoCard));
        }
        if (testPropertySet(1)) {
            config->setValue(QStringLiteral("soundOutputSlider"), QVariant::fromValue(p_soundOutputSlider));
        } else {
            updateValue(QStringLiteral("soundOutputSlider"), QVariant::fromValue(p_soundOutputSlider));
        }

        connect(config, &DTK_CORE_NAMESPACE::DConfig::valueChanged, this, [this](const QString &key) {
            updateValue(key);
        }, Qt::DirectConnection);
    }
    void updateValue(const QString &key, const QVariant &fallback = QVariant()) {
        Q_ASSERT(QThread::currentThread() == m_config.loadRelaxed()->thread());
        const QVariant &value = m_config.loadRelaxed()->value(key, fallback);
        if (key == QStringLiteral("enableAdjustVolumeNoCard")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_enableAdjustVolumeNoCard != newValue) {
                    p_enableAdjustVolumeNoCard = newValue;
                    Q_EMIT enableAdjustVolumeNoCardChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("soundOutputSlider")) {
            auto newValue = qvariant_cast<double>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_soundOutputSlider != newValue) {
                    p_soundOutputSlider = newValue;
                    Q_EMIT soundOutputSliderChanged();
                }
            });
            return;
        }
    }
    inline void markPropertySet(const int index) {
        if (index < 32) {
            m_propertySetStatus0.fetchAndOrOrdered(1 << (index - 0));
            return;
        }
        Q_UNREACHABLE();
    }
    inline bool testPropertySet(const int index) const {
        if (index < 32) {
            return (m_propertySetStatus0.loadRelaxed() & (1 << (index - 0)));
        }
        Q_UNREACHABLE();
    }
    QAtomicPointer<DTK_CORE_NAMESPACE::DConfig> m_config = nullptr;
    bool p_enableAdjustVolumeNoCard { false };
    double p_soundOutputSlider { 0 };
    QAtomicInteger<quint32> m_propertySetStatus0 = 0;
};

#endif // ORG_DEEPIN_DDE_DOCK_PLUGIN_SOUND_H
