/**
 * This file is generated by dconfig2cpp.
 * Command line arguments: ./dconfig2cpp -p ./dde-tray-loader/toolGenerate/dconfig2cpp ./dde-tray-loader/plugins/dde-dock/configs/org.deepin.dde.dock.plugin.quick-panel.json
 * Generation time: 2025-01-14T10:55:03
 * JSON file version: 1.0
 * 
 * WARNING: DO NOT MODIFY THIS FILE MANUALLY.
 * If you need to change the content, please modify the dconfig2cpp tool.
 */

#ifndef ORG_DEEPIN_DDE_DOCK_PLUGIN_QUICK-PANEL_H
#define ORG_DEEPIN_DDE_DOCK_PLUGIN_QUICK-PANEL_H

#include <QThread>
#include <QVariant>
#include <QDebug>
#include <QAtomicPointer>
#include <QAtomicInteger>
#include <DConfig>

class org_deepin_dde_dock_plugin_quick-panel : public QObject {
    Q_OBJECT

    Q_PROPERTY(QList<QVariant> dockedQuickPlugins READ dockedQuickPlugins WRITE setDockedQuickPlugins NOTIFY dockedQuickPluginsChanged)
    Q_PROPERTY(QList<QVariant> hiddenQuickPlugins READ hiddenQuickPlugins WRITE setHiddenQuickPlugins NOTIFY hiddenQuickPluginsChanged)
    Q_PROPERTY(QList<QVariant> quickPluginsOrder READ quickPluginsOrder WRITE setQuickPluginsOrder NOTIFY quickPluginsOrderChanged)
public:
    explicit org_deepin_dde_dock_plugin_quick-panel(QThread *thread, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_dock_plugin_quick-panel(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_dock_plugin_quick-panel(QThread *thread, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_dock_plugin_quick-panel(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    ~org_deepin_dde_dock_plugin_quick-panel() {
        if (m_config.loadRelaxed()) {
            m_config.loadRelaxed()->deleteLater();
        }
    }

    QList<QVariant> dockedQuickPlugins() const {
        return p_dockedQuickPlugins;
    }
    void setDockedQuickPlugins(const QList<QVariant> &value) {
        auto oldValue = p_dockedQuickPlugins;
        p_dockedQuickPlugins = value;
        markPropertySet(0);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("dockedQuickPlugins"), value);
            });
        }
        if (p_dockedQuickPlugins != oldValue) {
            Q_EMIT dockedQuickPluginsChanged();
        }
    }
    QList<QVariant> hiddenQuickPlugins() const {
        return p_hiddenQuickPlugins;
    }
    void setHiddenQuickPlugins(const QList<QVariant> &value) {
        auto oldValue = p_hiddenQuickPlugins;
        p_hiddenQuickPlugins = value;
        markPropertySet(1);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("hiddenQuickPlugins"), value);
            });
        }
        if (p_hiddenQuickPlugins != oldValue) {
            Q_EMIT hiddenQuickPluginsChanged();
        }
    }
    QList<QVariant> quickPluginsOrder() const {
        return p_quickPluginsOrder;
    }
    void setQuickPluginsOrder(const QList<QVariant> &value) {
        auto oldValue = p_quickPluginsOrder;
        p_quickPluginsOrder = value;
        markPropertySet(2);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("quickPluginsOrder"), value);
            });
        }
        if (p_quickPluginsOrder != oldValue) {
            Q_EMIT quickPluginsOrderChanged();
        }
    }
Q_SIGNALS:
    void dockedQuickPluginsChanged();
    void hiddenQuickPluginsChanged();
    void quickPluginsOrderChanged();
private:
    void initialize(DTK_CORE_NAMESPACE::DConfig *config) {
        Q_ASSERT(!m_config.loadRelaxed());
        m_config.storeRelaxed(config);
        if (testPropertySet(0)) {
            config->setValue(QStringLiteral("dockedQuickPlugins"), QVariant::fromValue(p_dockedQuickPlugins));
        } else {
            updateValue(QStringLiteral("dockedQuickPlugins"), QVariant::fromValue(p_dockedQuickPlugins));
        }
        if (testPropertySet(1)) {
            config->setValue(QStringLiteral("hiddenQuickPlugins"), QVariant::fromValue(p_hiddenQuickPlugins));
        } else {
            updateValue(QStringLiteral("hiddenQuickPlugins"), QVariant::fromValue(p_hiddenQuickPlugins));
        }
        if (testPropertySet(2)) {
            config->setValue(QStringLiteral("quickPluginsOrder"), QVariant::fromValue(p_quickPluginsOrder));
        } else {
            updateValue(QStringLiteral("quickPluginsOrder"), QVariant::fromValue(p_quickPluginsOrder));
        }

        connect(config, &DTK_CORE_NAMESPACE::DConfig::valueChanged, this, [this](const QString &key) {
            updateValue(key);
        }, Qt::DirectConnection);
    }
    void updateValue(const QString &key, const QVariant &fallback = QVariant()) {
        Q_ASSERT(QThread::currentThread() == m_config.loadRelaxed()->thread());
        const QVariant &value = m_config.loadRelaxed()->value(key, fallback);
        if (key == QStringLiteral("dockedQuickPlugins")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_dockedQuickPlugins != newValue) {
                    p_dockedQuickPlugins = newValue;
                    Q_EMIT dockedQuickPluginsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("hiddenQuickPlugins")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_hiddenQuickPlugins != newValue) {
                    p_hiddenQuickPlugins = newValue;
                    Q_EMIT hiddenQuickPluginsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("quickPluginsOrder")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_quickPluginsOrder != newValue) {
                    p_quickPluginsOrder = newValue;
                    Q_EMIT quickPluginsOrderChanged();
                }
            });
            return;
        }
    }
    inline void markPropertySet(const int index) {
        if (index < 32) {
            m_propertySetStatus0.fetchAndOrOrdered(1 << (index - 0));
            return;
        }
        Q_UNREACHABLE();
    }
    inline bool testPropertySet(const int index) const {
        if (index < 32) {
            return (m_propertySetStatus0.loadRelaxed() & (1 << (index - 0)));
        }
        Q_UNREACHABLE();
    }
    QAtomicPointer<DTK_CORE_NAMESPACE::DConfig> m_config = nullptr;
    QList<QVariant> p_dockedQuickPlugins { QList<QVariant>{QVariant(QStringLiteral("network"))} };
    QList<QVariant> p_hiddenQuickPlugins { QList<QVariant>{} };
    QList<QVariant> p_quickPluginsOrder { QList<QVariant>{QVariant(QStringLiteral("network")), QVariant(QStringLiteral("bluetooth")), QVariant(QStringLiteral("wireless-casting")), QVariant(QStringLiteral("grand-search")), QVariant(QStringLiteral("eye-comfort-mode")), QVariant(QStringLiteral("airplane-mode")), QVariant(QStringLiteral("dnd-mode")), QVariant(QStringLiteral("shot-start-plugin")), QVariant(QStringLiteral("shot-start-record-plugin")), QVariant(QStringLiteral("clipboard")), QVariant(QStringLiteral("system-monitor")), QVariant(QStringLiteral("media")), QVariant(QStringLiteral("dde-brightness")), QVariant(QStringLiteral("sound"))} };
    QAtomicInteger<quint32> m_propertySetStatus0 = 0;
};

#endif // ORG_DEEPIN_DDE_DOCK_PLUGIN_QUICK-PANEL_H
