/**
 * This file is generated by dconfig2cpp.
 * Command line arguments: ./dconfig2cpp -p ./dde-tray-loader/toolGenerate/dconfig2cpp ./dde-tray-loader/plugins/dde-network-core/config/org.deepin.dde.network.json
 * Generation time: 2025-01-14T10:55:03
 * JSON file version: 1.0
 * 
 * WARNING: DO NOT MODIFY THIS FILE MANUALLY.
 * If you need to change the content, please modify the dconfig2cpp tool.
 */

#ifndef ORG_DEEPIN_DDE_NETWORK_H
#define ORG_DEEPIN_DDE_NETWORK_H

#include <QThread>
#include <QVariant>
#include <QDebug>
#include <QAtomicPointer>
#include <QAtomicInteger>
#include <DConfig>

class org_deepin_dde_network : public QObject {
    Q_OBJECT

    Q_PROPERTY(double ConnectivityCheckInterval READ ConnectivityCheckInterval WRITE setConnectivityCheckInterval NOTIFY ConnectivityCheckIntervalChanged)
    Q_PROPERTY(bool LoadServiceFromNM READ LoadServiceFromNM WRITE setLoadServiceFromNM NOTIFY LoadServiceFromNMChanged)
    Q_PROPERTY(QList<QVariant> NetworkCheckerUrls READ NetworkCheckerUrls WRITE setNetworkCheckerUrls NOTIFY NetworkCheckerUrlsChanged)
    Q_PROPERTY(QList<QVariant> WLANScanInterval READ WLANScanInterval WRITE setWLANScanInterval NOTIFY WLANScanIntervalChanged)
    Q_PROPERTY(bool WPA3-Enterprise-Visible READ WPA3-Enterprise-Visible WRITE setWPA3-Enterprise-Visible NOTIFY WPA3-Enterprise-VisibleChanged)
    Q_PROPERTY(QString browserUrl READ browserUrl WRITE setBrowserUrl NOTIFY browserUrlChanged)
    Q_PROPERTY(bool checkPortal READ checkPortal WRITE setCheckPortal NOTIFY checkPortalChanged)
    Q_PROPERTY(bool disabledNetwork READ disabledNetwork WRITE setDisabledNetwork NOTIFY disabledNetworkChanged)
    Q_PROPERTY(bool dontSetIpIfConflict READ dontSetIpIfConflict WRITE setDontSetIpIfConflict NOTIFY dontSetIpIfConflictChanged)
    Q_PROPERTY(bool enableAccountNetwork READ enableAccountNetwork WRITE setEnableAccountNetwork NOTIFY enableAccountNetworkChanged)
    Q_PROPERTY(bool enableConnectivity READ enableConnectivity WRITE setEnableConnectivity NOTIFY enableConnectivityChanged)
    Q_PROPERTY(bool enableEapInput READ enableEapInput WRITE setEnableEapInput NOTIFY enableEapInputChanged)
    Q_PROPERTY(QString lastProxyMethod READ lastProxyMethod WRITE setLastProxyMethod NOTIFY lastProxyMethodChanged)
    Q_PROPERTY(bool networkAirplaneMode READ networkAirplaneMode WRITE setNetworkAirplaneMode NOTIFY networkAirplaneModeChanged)
    Q_PROPERTY(bool reconnectIfIpConflicted READ reconnectIfIpConflicted WRITE setReconnectIfIpConflicted NOTIFY reconnectIfIpConflictedChanged)
    Q_PROPERTY(bool showBrowserLink READ showBrowserLink WRITE setShowBrowserLink NOTIFY showBrowserLinkChanged)
    Q_PROPERTY(bool showUnauthorizedSwitch READ showUnauthorizedSwitch WRITE setShowUnauthorizedSwitch NOTIFY showUnauthorizedSwitchChanged)
    Q_PROPERTY(bool supportCertifiedEscape READ supportCertifiedEscape WRITE setSupportCertifiedEscape NOTIFY supportCertifiedEscapeChanged)
    Q_PROPERTY(double wirelessScanInterval READ wirelessScanInterval WRITE setWirelessScanInterval NOTIFY wirelessScanIntervalChanged)
    Q_PROPERTY(QString wpaEapAuthen READ wpaEapAuthen WRITE setWpaEapAuthen NOTIFY wpaEapAuthenChanged)
    Q_PROPERTY(QString wpaEapAuthmethod READ wpaEapAuthmethod WRITE setWpaEapAuthmethod NOTIFY wpaEapAuthmethodChanged)
public:
    explicit org_deepin_dde_network(QThread *thread, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_network(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_network(QThread *thread, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_network(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    ~org_deepin_dde_network() {
        if (m_config.loadRelaxed()) {
            m_config.loadRelaxed()->deleteLater();
        }
    }

    double ConnectivityCheckInterval() const {
        return p_ConnectivityCheckInterval;
    }
    void setConnectivityCheckInterval(const double &value) {
        auto oldValue = p_ConnectivityCheckInterval;
        p_ConnectivityCheckInterval = value;
        markPropertySet(0);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("ConnectivityCheckInterval"), value);
            });
        }
        if (p_ConnectivityCheckInterval != oldValue) {
            Q_EMIT ConnectivityCheckIntervalChanged();
        }
    }
    bool LoadServiceFromNM() const {
        return p_LoadServiceFromNM;
    }
    void setLoadServiceFromNM(const bool &value) {
        auto oldValue = p_LoadServiceFromNM;
        p_LoadServiceFromNM = value;
        markPropertySet(1);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("LoadServiceFromNM"), value);
            });
        }
        if (p_LoadServiceFromNM != oldValue) {
            Q_EMIT LoadServiceFromNMChanged();
        }
    }
    QList<QVariant> NetworkCheckerUrls() const {
        return p_NetworkCheckerUrls;
    }
    void setNetworkCheckerUrls(const QList<QVariant> &value) {
        auto oldValue = p_NetworkCheckerUrls;
        p_NetworkCheckerUrls = value;
        markPropertySet(2);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("NetworkCheckerUrls"), value);
            });
        }
        if (p_NetworkCheckerUrls != oldValue) {
            Q_EMIT NetworkCheckerUrlsChanged();
        }
    }
    QList<QVariant> WLANScanInterval() const {
        return p_WLANScanInterval;
    }
    void setWLANScanInterval(const QList<QVariant> &value) {
        auto oldValue = p_WLANScanInterval;
        p_WLANScanInterval = value;
        markPropertySet(3);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("WLANScanInterval"), value);
            });
        }
        if (p_WLANScanInterval != oldValue) {
            Q_EMIT WLANScanIntervalChanged();
        }
    }
    bool WPA3-Enterprise-Visible() const {
        return p_WPA3-Enterprise-Visible;
    }
    void setWPA3-Enterprise-Visible(const bool &value) {
        auto oldValue = p_WPA3-Enterprise-Visible;
        p_WPA3-Enterprise-Visible = value;
        markPropertySet(4);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("WPA3-Enterprise-Visible"), value);
            });
        }
        if (p_WPA3-Enterprise-Visible != oldValue) {
            Q_EMIT WPA3-Enterprise-VisibleChanged();
        }
    }
    QString browserUrl() const {
        return p_browserUrl;
    }
    void setBrowserUrl(const QString &value) {
        auto oldValue = p_browserUrl;
        p_browserUrl = value;
        markPropertySet(5);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("browserUrl"), value);
            });
        }
        if (p_browserUrl != oldValue) {
            Q_EMIT browserUrlChanged();
        }
    }
    bool checkPortal() const {
        return p_checkPortal;
    }
    void setCheckPortal(const bool &value) {
        auto oldValue = p_checkPortal;
        p_checkPortal = value;
        markPropertySet(6);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("checkPortal"), value);
            });
        }
        if (p_checkPortal != oldValue) {
            Q_EMIT checkPortalChanged();
        }
    }
    bool disabledNetwork() const {
        return p_disabledNetwork;
    }
    void setDisabledNetwork(const bool &value) {
        auto oldValue = p_disabledNetwork;
        p_disabledNetwork = value;
        markPropertySet(7);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("disabledNetwork"), value);
            });
        }
        if (p_disabledNetwork != oldValue) {
            Q_EMIT disabledNetworkChanged();
        }
    }
    bool dontSetIpIfConflict() const {
        return p_dontSetIpIfConflict;
    }
    void setDontSetIpIfConflict(const bool &value) {
        auto oldValue = p_dontSetIpIfConflict;
        p_dontSetIpIfConflict = value;
        markPropertySet(8);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("dontSetIpIfConflict"), value);
            });
        }
        if (p_dontSetIpIfConflict != oldValue) {
            Q_EMIT dontSetIpIfConflictChanged();
        }
    }
    bool enableAccountNetwork() const {
        return p_enableAccountNetwork;
    }
    void setEnableAccountNetwork(const bool &value) {
        auto oldValue = p_enableAccountNetwork;
        p_enableAccountNetwork = value;
        markPropertySet(9);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("enableAccountNetwork"), value);
            });
        }
        if (p_enableAccountNetwork != oldValue) {
            Q_EMIT enableAccountNetworkChanged();
        }
    }
    bool enableConnectivity() const {
        return p_enableConnectivity;
    }
    void setEnableConnectivity(const bool &value) {
        auto oldValue = p_enableConnectivity;
        p_enableConnectivity = value;
        markPropertySet(10);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("enableConnectivity"), value);
            });
        }
        if (p_enableConnectivity != oldValue) {
            Q_EMIT enableConnectivityChanged();
        }
    }
    bool enableEapInput() const {
        return p_enableEapInput;
    }
    void setEnableEapInput(const bool &value) {
        auto oldValue = p_enableEapInput;
        p_enableEapInput = value;
        markPropertySet(11);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("enableEapInput"), value);
            });
        }
        if (p_enableEapInput != oldValue) {
            Q_EMIT enableEapInputChanged();
        }
    }
    QString lastProxyMethod() const {
        return p_lastProxyMethod;
    }
    void setLastProxyMethod(const QString &value) {
        auto oldValue = p_lastProxyMethod;
        p_lastProxyMethod = value;
        markPropertySet(12);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("lastProxyMethod"), value);
            });
        }
        if (p_lastProxyMethod != oldValue) {
            Q_EMIT lastProxyMethodChanged();
        }
    }
    bool networkAirplaneMode() const {
        return p_networkAirplaneMode;
    }
    void setNetworkAirplaneMode(const bool &value) {
        auto oldValue = p_networkAirplaneMode;
        p_networkAirplaneMode = value;
        markPropertySet(13);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("networkAirplaneMode"), value);
            });
        }
        if (p_networkAirplaneMode != oldValue) {
            Q_EMIT networkAirplaneModeChanged();
        }
    }
    bool reconnectIfIpConflicted() const {
        return p_reconnectIfIpConflicted;
    }
    void setReconnectIfIpConflicted(const bool &value) {
        auto oldValue = p_reconnectIfIpConflicted;
        p_reconnectIfIpConflicted = value;
        markPropertySet(14);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("reconnectIfIpConflicted"), value);
            });
        }
        if (p_reconnectIfIpConflicted != oldValue) {
            Q_EMIT reconnectIfIpConflictedChanged();
        }
    }
    bool showBrowserLink() const {
        return p_showBrowserLink;
    }
    void setShowBrowserLink(const bool &value) {
        auto oldValue = p_showBrowserLink;
        p_showBrowserLink = value;
        markPropertySet(15);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("showBrowserLink"), value);
            });
        }
        if (p_showBrowserLink != oldValue) {
            Q_EMIT showBrowserLinkChanged();
        }
    }
    bool showUnauthorizedSwitch() const {
        return p_showUnauthorizedSwitch;
    }
    void setShowUnauthorizedSwitch(const bool &value) {
        auto oldValue = p_showUnauthorizedSwitch;
        p_showUnauthorizedSwitch = value;
        markPropertySet(16);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("showUnauthorizedSwitch"), value);
            });
        }
        if (p_showUnauthorizedSwitch != oldValue) {
            Q_EMIT showUnauthorizedSwitchChanged();
        }
    }
    bool supportCertifiedEscape() const {
        return p_supportCertifiedEscape;
    }
    void setSupportCertifiedEscape(const bool &value) {
        auto oldValue = p_supportCertifiedEscape;
        p_supportCertifiedEscape = value;
        markPropertySet(17);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("supportCertifiedEscape"), value);
            });
        }
        if (p_supportCertifiedEscape != oldValue) {
            Q_EMIT supportCertifiedEscapeChanged();
        }
    }
    double wirelessScanInterval() const {
        return p_wirelessScanInterval;
    }
    void setWirelessScanInterval(const double &value) {
        auto oldValue = p_wirelessScanInterval;
        p_wirelessScanInterval = value;
        markPropertySet(18);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("wirelessScanInterval"), value);
            });
        }
        if (p_wirelessScanInterval != oldValue) {
            Q_EMIT wirelessScanIntervalChanged();
        }
    }
    QString wpaEapAuthen() const {
        return p_wpaEapAuthen;
    }
    void setWpaEapAuthen(const QString &value) {
        auto oldValue = p_wpaEapAuthen;
        p_wpaEapAuthen = value;
        markPropertySet(19);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("wpaEapAuthen"), value);
            });
        }
        if (p_wpaEapAuthen != oldValue) {
            Q_EMIT wpaEapAuthenChanged();
        }
    }
    QString wpaEapAuthmethod() const {
        return p_wpaEapAuthmethod;
    }
    void setWpaEapAuthmethod(const QString &value) {
        auto oldValue = p_wpaEapAuthmethod;
        p_wpaEapAuthmethod = value;
        markPropertySet(20);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("wpaEapAuthmethod"), value);
            });
        }
        if (p_wpaEapAuthmethod != oldValue) {
            Q_EMIT wpaEapAuthmethodChanged();
        }
    }
Q_SIGNALS:
    void ConnectivityCheckIntervalChanged();
    void LoadServiceFromNMChanged();
    void NetworkCheckerUrlsChanged();
    void WLANScanIntervalChanged();
    void WPA3-Enterprise-VisibleChanged();
    void browserUrlChanged();
    void checkPortalChanged();
    void disabledNetworkChanged();
    void dontSetIpIfConflictChanged();
    void enableAccountNetworkChanged();
    void enableConnectivityChanged();
    void enableEapInputChanged();
    void lastProxyMethodChanged();
    void networkAirplaneModeChanged();
    void reconnectIfIpConflictedChanged();
    void showBrowserLinkChanged();
    void showUnauthorizedSwitchChanged();
    void supportCertifiedEscapeChanged();
    void wirelessScanIntervalChanged();
    void wpaEapAuthenChanged();
    void wpaEapAuthmethodChanged();
private:
    void initialize(DTK_CORE_NAMESPACE::DConfig *config) {
        Q_ASSERT(!m_config.loadRelaxed());
        m_config.storeRelaxed(config);
        if (testPropertySet(0)) {
            config->setValue(QStringLiteral("ConnectivityCheckInterval"), QVariant::fromValue(p_ConnectivityCheckInterval));
        } else {
            updateValue(QStringLiteral("ConnectivityCheckInterval"), QVariant::fromValue(p_ConnectivityCheckInterval));
        }
        if (testPropertySet(1)) {
            config->setValue(QStringLiteral("LoadServiceFromNM"), QVariant::fromValue(p_LoadServiceFromNM));
        } else {
            updateValue(QStringLiteral("LoadServiceFromNM"), QVariant::fromValue(p_LoadServiceFromNM));
        }
        if (testPropertySet(2)) {
            config->setValue(QStringLiteral("NetworkCheckerUrls"), QVariant::fromValue(p_NetworkCheckerUrls));
        } else {
            updateValue(QStringLiteral("NetworkCheckerUrls"), QVariant::fromValue(p_NetworkCheckerUrls));
        }
        if (testPropertySet(3)) {
            config->setValue(QStringLiteral("WLANScanInterval"), QVariant::fromValue(p_WLANScanInterval));
        } else {
            updateValue(QStringLiteral("WLANScanInterval"), QVariant::fromValue(p_WLANScanInterval));
        }
        if (testPropertySet(4)) {
            config->setValue(QStringLiteral("WPA3-Enterprise-Visible"), QVariant::fromValue(p_WPA3-Enterprise-Visible));
        } else {
            updateValue(QStringLiteral("WPA3-Enterprise-Visible"), QVariant::fromValue(p_WPA3-Enterprise-Visible));
        }
        if (testPropertySet(5)) {
            config->setValue(QStringLiteral("browserUrl"), QVariant::fromValue(p_browserUrl));
        } else {
            updateValue(QStringLiteral("browserUrl"), QVariant::fromValue(p_browserUrl));
        }
        if (testPropertySet(6)) {
            config->setValue(QStringLiteral("checkPortal"), QVariant::fromValue(p_checkPortal));
        } else {
            updateValue(QStringLiteral("checkPortal"), QVariant::fromValue(p_checkPortal));
        }
        if (testPropertySet(7)) {
            config->setValue(QStringLiteral("disabledNetwork"), QVariant::fromValue(p_disabledNetwork));
        } else {
            updateValue(QStringLiteral("disabledNetwork"), QVariant::fromValue(p_disabledNetwork));
        }
        if (testPropertySet(8)) {
            config->setValue(QStringLiteral("dontSetIpIfConflict"), QVariant::fromValue(p_dontSetIpIfConflict));
        } else {
            updateValue(QStringLiteral("dontSetIpIfConflict"), QVariant::fromValue(p_dontSetIpIfConflict));
        }
        if (testPropertySet(9)) {
            config->setValue(QStringLiteral("enableAccountNetwork"), QVariant::fromValue(p_enableAccountNetwork));
        } else {
            updateValue(QStringLiteral("enableAccountNetwork"), QVariant::fromValue(p_enableAccountNetwork));
        }
        if (testPropertySet(10)) {
            config->setValue(QStringLiteral("enableConnectivity"), QVariant::fromValue(p_enableConnectivity));
        } else {
            updateValue(QStringLiteral("enableConnectivity"), QVariant::fromValue(p_enableConnectivity));
        }
        if (testPropertySet(11)) {
            config->setValue(QStringLiteral("enableEapInput"), QVariant::fromValue(p_enableEapInput));
        } else {
            updateValue(QStringLiteral("enableEapInput"), QVariant::fromValue(p_enableEapInput));
        }
        if (testPropertySet(12)) {
            config->setValue(QStringLiteral("lastProxyMethod"), QVariant::fromValue(p_lastProxyMethod));
        } else {
            updateValue(QStringLiteral("lastProxyMethod"), QVariant::fromValue(p_lastProxyMethod));
        }
        if (testPropertySet(13)) {
            config->setValue(QStringLiteral("networkAirplaneMode"), QVariant::fromValue(p_networkAirplaneMode));
        } else {
            updateValue(QStringLiteral("networkAirplaneMode"), QVariant::fromValue(p_networkAirplaneMode));
        }
        if (testPropertySet(14)) {
            config->setValue(QStringLiteral("reconnectIfIpConflicted"), QVariant::fromValue(p_reconnectIfIpConflicted));
        } else {
            updateValue(QStringLiteral("reconnectIfIpConflicted"), QVariant::fromValue(p_reconnectIfIpConflicted));
        }
        if (testPropertySet(15)) {
            config->setValue(QStringLiteral("showBrowserLink"), QVariant::fromValue(p_showBrowserLink));
        } else {
            updateValue(QStringLiteral("showBrowserLink"), QVariant::fromValue(p_showBrowserLink));
        }
        if (testPropertySet(16)) {
            config->setValue(QStringLiteral("showUnauthorizedSwitch"), QVariant::fromValue(p_showUnauthorizedSwitch));
        } else {
            updateValue(QStringLiteral("showUnauthorizedSwitch"), QVariant::fromValue(p_showUnauthorizedSwitch));
        }
        if (testPropertySet(17)) {
            config->setValue(QStringLiteral("supportCertifiedEscape"), QVariant::fromValue(p_supportCertifiedEscape));
        } else {
            updateValue(QStringLiteral("supportCertifiedEscape"), QVariant::fromValue(p_supportCertifiedEscape));
        }
        if (testPropertySet(18)) {
            config->setValue(QStringLiteral("wirelessScanInterval"), QVariant::fromValue(p_wirelessScanInterval));
        } else {
            updateValue(QStringLiteral("wirelessScanInterval"), QVariant::fromValue(p_wirelessScanInterval));
        }
        if (testPropertySet(19)) {
            config->setValue(QStringLiteral("wpaEapAuthen"), QVariant::fromValue(p_wpaEapAuthen));
        } else {
            updateValue(QStringLiteral("wpaEapAuthen"), QVariant::fromValue(p_wpaEapAuthen));
        }
        if (testPropertySet(20)) {
            config->setValue(QStringLiteral("wpaEapAuthmethod"), QVariant::fromValue(p_wpaEapAuthmethod));
        } else {
            updateValue(QStringLiteral("wpaEapAuthmethod"), QVariant::fromValue(p_wpaEapAuthmethod));
        }

        connect(config, &DTK_CORE_NAMESPACE::DConfig::valueChanged, this, [this](const QString &key) {
            updateValue(key);
        }, Qt::DirectConnection);
    }
    void updateValue(const QString &key, const QVariant &fallback = QVariant()) {
        Q_ASSERT(QThread::currentThread() == m_config.loadRelaxed()->thread());
        const QVariant &value = m_config.loadRelaxed()->value(key, fallback);
        if (key == QStringLiteral("ConnectivityCheckInterval")) {
            auto newValue = qvariant_cast<double>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_ConnectivityCheckInterval != newValue) {
                    p_ConnectivityCheckInterval = newValue;
                    Q_EMIT ConnectivityCheckIntervalChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("LoadServiceFromNM")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_LoadServiceFromNM != newValue) {
                    p_LoadServiceFromNM = newValue;
                    Q_EMIT LoadServiceFromNMChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("NetworkCheckerUrls")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_NetworkCheckerUrls != newValue) {
                    p_NetworkCheckerUrls = newValue;
                    Q_EMIT NetworkCheckerUrlsChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("WLANScanInterval")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_WLANScanInterval != newValue) {
                    p_WLANScanInterval = newValue;
                    Q_EMIT WLANScanIntervalChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("WPA3-Enterprise-Visible")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_WPA3-Enterprise-Visible != newValue) {
                    p_WPA3-Enterprise-Visible = newValue;
                    Q_EMIT WPA3-Enterprise-VisibleChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("browserUrl")) {
            auto newValue = qvariant_cast<QString>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_browserUrl != newValue) {
                    p_browserUrl = newValue;
                    Q_EMIT browserUrlChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("checkPortal")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_checkPortal != newValue) {
                    p_checkPortal = newValue;
                    Q_EMIT checkPortalChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("disabledNetwork")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_disabledNetwork != newValue) {
                    p_disabledNetwork = newValue;
                    Q_EMIT disabledNetworkChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("dontSetIpIfConflict")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_dontSetIpIfConflict != newValue) {
                    p_dontSetIpIfConflict = newValue;
                    Q_EMIT dontSetIpIfConflictChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("enableAccountNetwork")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_enableAccountNetwork != newValue) {
                    p_enableAccountNetwork = newValue;
                    Q_EMIT enableAccountNetworkChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("enableConnectivity")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_enableConnectivity != newValue) {
                    p_enableConnectivity = newValue;
                    Q_EMIT enableConnectivityChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("enableEapInput")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_enableEapInput != newValue) {
                    p_enableEapInput = newValue;
                    Q_EMIT enableEapInputChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("lastProxyMethod")) {
            auto newValue = qvariant_cast<QString>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_lastProxyMethod != newValue) {
                    p_lastProxyMethod = newValue;
                    Q_EMIT lastProxyMethodChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("networkAirplaneMode")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_networkAirplaneMode != newValue) {
                    p_networkAirplaneMode = newValue;
                    Q_EMIT networkAirplaneModeChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("reconnectIfIpConflicted")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_reconnectIfIpConflicted != newValue) {
                    p_reconnectIfIpConflicted = newValue;
                    Q_EMIT reconnectIfIpConflictedChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("showBrowserLink")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_showBrowserLink != newValue) {
                    p_showBrowserLink = newValue;
                    Q_EMIT showBrowserLinkChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("showUnauthorizedSwitch")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_showUnauthorizedSwitch != newValue) {
                    p_showUnauthorizedSwitch = newValue;
                    Q_EMIT showUnauthorizedSwitchChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("supportCertifiedEscape")) {
            auto newValue = qvariant_cast<bool>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_supportCertifiedEscape != newValue) {
                    p_supportCertifiedEscape = newValue;
                    Q_EMIT supportCertifiedEscapeChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("wirelessScanInterval")) {
            auto newValue = qvariant_cast<double>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_wirelessScanInterval != newValue) {
                    p_wirelessScanInterval = newValue;
                    Q_EMIT wirelessScanIntervalChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("wpaEapAuthen")) {
            auto newValue = qvariant_cast<QString>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_wpaEapAuthen != newValue) {
                    p_wpaEapAuthen = newValue;
                    Q_EMIT wpaEapAuthenChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("wpaEapAuthmethod")) {
            auto newValue = qvariant_cast<QString>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_wpaEapAuthmethod != newValue) {
                    p_wpaEapAuthmethod = newValue;
                    Q_EMIT wpaEapAuthmethodChanged();
                }
            });
            return;
        }
    }
    inline void markPropertySet(const int index) {
        if (index < 32) {
            m_propertySetStatus0.fetchAndOrOrdered(1 << (index - 0));
            return;
        }
        Q_UNREACHABLE();
    }
    inline bool testPropertySet(const int index) const {
        if (index < 32) {
            return (m_propertySetStatus0.loadRelaxed() & (1 << (index - 0)));
        }
        Q_UNREACHABLE();
    }
    QAtomicPointer<DTK_CORE_NAMESPACE::DConfig> m_config = nullptr;
    double p_ConnectivityCheckInterval { 30 };
    bool p_LoadServiceFromNM { true };
    QList<QVariant> p_NetworkCheckerUrls { QList<QVariant>{QVariant(QStringLiteral("http://detect.uniontech.com")), QVariant(QStringLiteral("http://detectportal.deepin.com"))} };
    QList<QVariant> p_WLANScanInterval { QList<QVariant>{QVariant(10), QVariant(10), QVariant(10), QVariant(30), QVariant(30), QVariant(30), QVariant(60)} };
    bool p_WPA3-Enterprise-Visible { false };
    QString p_browserUrl { QStringLiteral("https://www.uniontech.com") };
    bool p_checkPortal { true };
    bool p_disabledNetwork { false };
    bool p_dontSetIpIfConflict { false };
    bool p_enableAccountNetwork { false };
    bool p_enableConnectivity { true };
    bool p_enableEapInput { false };
    QString p_lastProxyMethod { QStringLiteral("manual") };
    bool p_networkAirplaneMode { true };
    bool p_reconnectIfIpConflicted { false };
    bool p_showBrowserLink { false };
    bool p_showUnauthorizedSwitch { false };
    bool p_supportCertifiedEscape { false };
    double p_wirelessScanInterval { 10 };
    QString p_wpaEapAuthen { QStringLiteral("peap") };
    QString p_wpaEapAuthmethod { QStringLiteral("MSCHAPV2") };
    QAtomicInteger<quint32> m_propertySetStatus0 = 0;
};

#endif // ORG_DEEPIN_DDE_NETWORK_H
