/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/application-tray/api/dbus/org.kde.StatusNotifierWatcher.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.kde.StatusNotifierWatcherAdaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.kde.StatusNotifierWatcher.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_KDE_STATUSNOTIFIERWATCHERADAPTOR_H
#define ORG_KDE_STATUSNOTIFIERWATCHERADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.kde.StatusNotifierWatcher.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.kde.StatusNotifierWatcher
 */
class StatusNotifierWatcherAdaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.kde.StatusNotifierWatcher")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.kde.StatusNotifierWatcher\">\n"
"    <method name=\"RegisterStatusNotifierHost\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"serviceName\"/>\n"
"    </method>\n"
"    <method name=\"RegisterStatusNotifierItem\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"serviceOrPath\"/>\n"
"    </method>\n"
"    <signal name=\"StatusNotifierItemRegistered\">\n"
"      <arg type=\"s\" name=\"ServiceName\"/>\n"
"    </signal>\n"
"    <signal name=\"StatusNotifierItemUnregistered\">\n"
"      <arg type=\"s\" name=\"ServiceName\"/>\n"
"    </signal>\n"
"    <signal name=\"StatusNotifierHostRegistered\"/>\n"
"    <property access=\"read\" type=\"as\" name=\"RegisteredStatusNotifierItems\"/>\n"
"    <property access=\"read\" type=\"b\" name=\"IsStatusNotifierHostRegistered\"/>\n"
"    <property access=\"read\" type=\"i\" name=\"ProtocolVersion\"/>\n"
"  </interface>\n"
        "")
public:
    StatusNotifierWatcherAdaptor(QObject *parent);
    virtual ~StatusNotifierWatcherAdaptor();

public: // PROPERTIES
    Q_PROPERTY(bool IsStatusNotifierHostRegistered READ isStatusNotifierHostRegistered)
    bool isStatusNotifierHostRegistered() const;

    Q_PROPERTY(int ProtocolVersion READ protocolVersion)
    int protocolVersion() const;

    Q_PROPERTY(QStringList RegisteredStatusNotifierItems READ registeredStatusNotifierItems)
    QStringList registeredStatusNotifierItems() const;

public Q_SLOTS: // METHODS
    void RegisterStatusNotifierHost(const QString &serviceName);
    void RegisterStatusNotifierItem(const QString &serviceOrPath);
Q_SIGNALS: // SIGNALS
    void StatusNotifierHostRegistered();
    void StatusNotifierItemRegistered(const QString &ServiceName);
    void StatusNotifierItemUnregistered(const QString &ServiceName);
};

#endif
