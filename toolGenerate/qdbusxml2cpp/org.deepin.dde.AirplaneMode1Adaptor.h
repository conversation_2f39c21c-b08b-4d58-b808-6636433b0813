/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.deepin.dde.AirplaneMode1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.AirplaneMode1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.AirplaneMode1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DEEPIN_DDE_AIRPLANEMODE1ADAPTOR_H
#define ORG_DEEPIN_DDE_AIRPLANEMODE1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.AirplaneMode1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.deepin.dde.AirplaneMode1
 */
class AirplaneMode1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.dde.AirplaneMode1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.dde.AirplaneMode1\">\n"
"    <method name=\"DumpState\"/>\n"
"    <method name=\"Enable\">\n"
"      <arg direction=\"in\" type=\"b\" name=\"enabled\"/>\n"
"    </method>\n"
"    <method name=\"EnableBluetooth\">\n"
"      <arg direction=\"in\" type=\"b\" name=\"enabled\"/>\n"
"    </method>\n"
"    <method name=\"EnableWifi\">\n"
"      <arg direction=\"in\" type=\"b\" name=\"enabled\"/>\n"
"    </method>\n"
"    <property access=\"read\" type=\"b\" name=\"BluetoothEnabled\"/>\n"
"    <property access=\"read\" type=\"b\" name=\"Enabled\"/>\n"
"    <property access=\"read\" type=\"b\" name=\"WifiEnabled\"/>\n"
"  </interface>\n"
        "")
public:
    AirplaneMode1Adaptor(QObject *parent);
    virtual ~AirplaneMode1Adaptor();

public: // PROPERTIES
    Q_PROPERTY(bool BluetoothEnabled READ bluetoothEnabled)
    bool bluetoothEnabled() const;

    Q_PROPERTY(bool Enabled READ enabled)
    bool enabled() const;

    Q_PROPERTY(bool WifiEnabled READ wifiEnabled)
    bool wifiEnabled() const;

public Q_SLOTS: // METHODS
    void DumpState();
    void Enable(bool enabled);
    void EnableBluetooth(bool enabled);
    void EnableWifi(bool enabled);
Q_SIGNALS: // SIGNALS
};

#endif
