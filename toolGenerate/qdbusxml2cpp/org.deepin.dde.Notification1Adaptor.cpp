/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.deepin.dde.Notification1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Notification1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Notification1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Notification1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class Notification1Adaptor
 */

Notification1Adaptor::Notification1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

Notification1Adaptor::~Notification1Adaptor()
{
    // destructor
}

QString Notification1Adaptor::allSetting() const
{
    // get the value of property allSetting
    return qvariant_cast< QString >(parent()->property("allSetting"));
}

void Notification1Adaptor::setAllSetting(const QString &value)
{
    // set the value of property allSetting
    parent()->setProperty("allSetting", QVariant::fromValue(value));
}

QString Notification1Adaptor::systemSetting() const
{
    // get the value of property systemSetting
    return qvariant_cast< QString >(parent()->property("systemSetting"));
}

void Notification1Adaptor::setSystemSetting(const QString &value)
{
    // set the value of property systemSetting
    parent()->setProperty("systemSetting", QVariant::fromValue(value));
}

void Notification1Adaptor::ClearRecords()
{
    // handle method call org.deepin.dde.Notification1.ClearRecords
    QMetaObject::invokeMethod(parent(), "ClearRecords");
}

void Notification1Adaptor::CloseNotification(uint in0)
{
    // handle method call org.deepin.dde.Notification1.CloseNotification
    QMetaObject::invokeMethod(parent(), "CloseNotification", Q_ARG(uint, in0));
}

QString Notification1Adaptor::GetAllRecords()
{
    // handle method call org.deepin.dde.Notification1.GetAllRecords
    QString out0;
    QMetaObject::invokeMethod(parent(), "GetAllRecords", Q_RETURN_ARG(QString, out0));
    return out0;
}

QDBusVariant Notification1Adaptor::GetAppInfo(const QString &in0, uint in1)
{
    // handle method call org.deepin.dde.Notification1.GetAppInfo
    QDBusVariant out0;
    QMetaObject::invokeMethod(parent(), "GetAppInfo", Q_RETURN_ARG(QDBusVariant, out0), Q_ARG(QString, in0), Q_ARG(uint, in1));
    return out0;
}

QStringList Notification1Adaptor::GetAppList()
{
    // handle method call org.deepin.dde.Notification1.GetAppList
    QStringList out0;
    QMetaObject::invokeMethod(parent(), "GetAppList", Q_RETURN_ARG(QStringList, out0));
    return out0;
}

QStringList Notification1Adaptor::GetCapbilities()
{
    // handle method call org.deepin.dde.Notification1.GetCapbilities
    QStringList out0;
    QMetaObject::invokeMethod(parent(), "GetCapbilities", Q_RETURN_ARG(QStringList, out0));
    return out0;
}

QString Notification1Adaptor::GetRecordById(const QString &in0)
{
    // handle method call org.deepin.dde.Notification1.GetRecordById
    QString out0;
    QMetaObject::invokeMethod(parent(), "GetRecordById", Q_RETURN_ARG(QString, out0), Q_ARG(QString, in0));
    return out0;
}

QString Notification1Adaptor::GetRecordsFromId(int in0, const QString &in1)
{
    // handle method call org.deepin.dde.Notification1.GetRecordsFromId
    QString out0;
    QMetaObject::invokeMethod(parent(), "GetRecordsFromId", Q_RETURN_ARG(QString, out0), Q_ARG(int, in0), Q_ARG(QString, in1));
    return out0;
}

QString Notification1Adaptor::GetServerInformation(QString &out1, QString &out2, QString &out3)
{
    // handle method call org.deepin.dde.Notification1.GetServerInformation
    //return static_cast<YourObjectType *>(parent())->GetServerInformation(out1, out2, out3);
}

QDBusVariant Notification1Adaptor::GetSystemInfo(uint in0)
{
    // handle method call org.deepin.dde.Notification1.GetSystemInfo
    QDBusVariant out0;
    QMetaObject::invokeMethod(parent(), "GetSystemInfo", Q_RETURN_ARG(QDBusVariant, out0), Q_ARG(uint, in0));
    return out0;
}

void Notification1Adaptor::Hide()
{
    // handle method call org.deepin.dde.Notification1.Hide
    QMetaObject::invokeMethod(parent(), "Hide");
}

uint Notification1Adaptor::Notify(const QString &in0, uint in1, const QString &in2, const QString &in3, const QString &in4, const QStringList &in5, const QVariantMap &in6, int in7)
{
    // handle method call org.deepin.dde.Notification1.Notify
    uint out0;
    QMetaObject::invokeMethod(parent(), "Notify", Q_RETURN_ARG(uint, out0), Q_ARG(QString, in0), Q_ARG(uint, in1), Q_ARG(QString, in2), Q_ARG(QString, in3), Q_ARG(QString, in4), Q_ARG(QStringList, in5), Q_ARG(QVariantMap, in6), Q_ARG(int, in7));
    return out0;
}

void Notification1Adaptor::RemoveRecord(const QString &in0)
{
    // handle method call org.deepin.dde.Notification1.RemoveRecord
    QMetaObject::invokeMethod(parent(), "RemoveRecord", Q_ARG(QString, in0));
}

void Notification1Adaptor::SetAppInfo(const QString &in0, uint in1, const QDBusVariant &in2)
{
    // handle method call org.deepin.dde.Notification1.SetAppInfo
    QMetaObject::invokeMethod(parent(), "SetAppInfo", Q_ARG(QString, in0), Q_ARG(uint, in1), Q_ARG(QDBusVariant, in2));
}

void Notification1Adaptor::SetSystemInfo(uint in0, const QDBusVariant &in1)
{
    // handle method call org.deepin.dde.Notification1.SetSystemInfo
    QMetaObject::invokeMethod(parent(), "SetSystemInfo", Q_ARG(uint, in0), Q_ARG(QDBusVariant, in1));
}

void Notification1Adaptor::Show()
{
    // handle method call org.deepin.dde.Notification1.Show
    QMetaObject::invokeMethod(parent(), "Show");
}

void Notification1Adaptor::Toggle()
{
    // handle method call org.deepin.dde.Notification1.Toggle
    QMetaObject::invokeMethod(parent(), "Toggle");
}

QString Notification1Adaptor::getAppSetting(const QString &in0)
{
    // handle method call org.deepin.dde.Notification1.getAppSetting
    QString out0;
    QMetaObject::invokeMethod(parent(), "getAppSetting", Q_RETURN_ARG(QString, out0), Q_ARG(QString, in0));
    return out0;
}

uint Notification1Adaptor::recordCount()
{
    // handle method call org.deepin.dde.Notification1.recordCount
    uint out0;
    QMetaObject::invokeMethod(parent(), "recordCount", Q_RETURN_ARG(uint, out0));
    return out0;
}

void Notification1Adaptor::setAppSetting(const QString &in0)
{
    // handle method call org.deepin.dde.Notification1.setAppSetting
    QMetaObject::invokeMethod(parent(), "setAppSetting", Q_ARG(QString, in0));
}

