/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.deepin.dde.Bluetooth1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Bluetooth1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Bluetooth1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Bluetooth1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class Bluetooth1Adaptor
 */

Bluetooth1Adaptor::Bluetooth1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

Bluetooth1Adaptor::~Bluetooth1Adaptor()
{
    // destructor
}

bool Bluetooth1Adaptor::canSendFile() const
{
    // get the value of property CanSendFile
    return qvariant_cast< bool >(parent()->property("CanSendFile"));
}

bool Bluetooth1Adaptor::displaySwitch() const
{
    // get the value of property DisplaySwitch
    return qvariant_cast< bool >(parent()->property("DisplaySwitch"));
}

void Bluetooth1Adaptor::setDisplaySwitch(bool value)
{
    // set the value of property DisplaySwitch
    parent()->setProperty("DisplaySwitch", QVariant::fromValue(value));
}

uint Bluetooth1Adaptor::state() const
{
    // get the value of property State
    return qvariant_cast< uint >(parent()->property("State"));
}

bool Bluetooth1Adaptor::transportable() const
{
    // get the value of property Transportable
    return qvariant_cast< bool >(parent()->property("Transportable"));
}

void Bluetooth1Adaptor::CancelTransferSession(const QDBusObjectPath &sessionPath)
{
    // handle method call org.deepin.dde.Bluetooth1.CancelTransferSession
    QMetaObject::invokeMethod(parent(), "CancelTransferSession", Q_ARG(QDBusObjectPath, sessionPath));
}

void Bluetooth1Adaptor::ClearUnpairedDevice()
{
    // handle method call org.deepin.dde.Bluetooth1.ClearUnpairedDevice
    QMetaObject::invokeMethod(parent(), "ClearUnpairedDevice");
}

void Bluetooth1Adaptor::Confirm(const QDBusObjectPath &device, bool accept)
{
    // handle method call org.deepin.dde.Bluetooth1.Confirm
    QMetaObject::invokeMethod(parent(), "Confirm", Q_ARG(QDBusObjectPath, device), Q_ARG(bool, accept));
}

void Bluetooth1Adaptor::ConnectDevice(const QDBusObjectPath &device, const QDBusObjectPath &adapter)
{
    // handle method call org.deepin.dde.Bluetooth1.ConnectDevice
    QMetaObject::invokeMethod(parent(), "ConnectDevice", Q_ARG(QDBusObjectPath, device), Q_ARG(QDBusObjectPath, adapter));
}

QString Bluetooth1Adaptor::DebugInfo()
{
    // handle method call org.deepin.dde.Bluetooth1.DebugInfo
    QString info;
    QMetaObject::invokeMethod(parent(), "DebugInfo", Q_RETURN_ARG(QString, info));
    return info;
}

void Bluetooth1Adaptor::DisconnectDevice(const QDBusObjectPath &device)
{
    // handle method call org.deepin.dde.Bluetooth1.DisconnectDevice
    QMetaObject::invokeMethod(parent(), "DisconnectDevice", Q_ARG(QDBusObjectPath, device));
}

void Bluetooth1Adaptor::FeedPasskey(const QDBusObjectPath &device, bool accept, uint passkey)
{
    // handle method call org.deepin.dde.Bluetooth1.FeedPasskey
    QMetaObject::invokeMethod(parent(), "FeedPasskey", Q_ARG(QDBusObjectPath, device), Q_ARG(bool, accept), Q_ARG(uint, passkey));
}

void Bluetooth1Adaptor::FeedPinCode(const QDBusObjectPath &device, bool accept, const QString &pinCode)
{
    // handle method call org.deepin.dde.Bluetooth1.FeedPinCode
    QMetaObject::invokeMethod(parent(), "FeedPinCode", Q_ARG(QDBusObjectPath, device), Q_ARG(bool, accept), Q_ARG(QString, pinCode));
}

QString Bluetooth1Adaptor::GetAdapters()
{
    // handle method call org.deepin.dde.Bluetooth1.GetAdapters
    QString adaptersJSON;
    QMetaObject::invokeMethod(parent(), "GetAdapters", Q_RETURN_ARG(QString, adaptersJSON));
    return adaptersJSON;
}

QString Bluetooth1Adaptor::GetDevices(const QDBusObjectPath &adapter)
{
    // handle method call org.deepin.dde.Bluetooth1.GetDevices
    QString devicesJSON;
    QMetaObject::invokeMethod(parent(), "GetDevices", Q_RETURN_ARG(QString, devicesJSON), Q_ARG(QDBusObjectPath, adapter));
    return devicesJSON;
}

void Bluetooth1Adaptor::RemoveDevice(const QDBusObjectPath &adapter, const QDBusObjectPath &device)
{
    // handle method call org.deepin.dde.Bluetooth1.RemoveDevice
    QMetaObject::invokeMethod(parent(), "RemoveDevice", Q_ARG(QDBusObjectPath, adapter), Q_ARG(QDBusObjectPath, device));
}

void Bluetooth1Adaptor::RequestDiscovery(const QDBusObjectPath &adapter)
{
    // handle method call org.deepin.dde.Bluetooth1.RequestDiscovery
    QMetaObject::invokeMethod(parent(), "RequestDiscovery", Q_ARG(QDBusObjectPath, adapter));
}

QDBusObjectPath Bluetooth1Adaptor::SendFiles(const QString &device, const QStringList &files)
{
    // handle method call org.deepin.dde.Bluetooth1.SendFiles
    QDBusObjectPath sessionPath;
    QMetaObject::invokeMethod(parent(), "SendFiles", Q_RETURN_ARG(QDBusObjectPath, sessionPath), Q_ARG(QString, device), Q_ARG(QStringList, files));
    return sessionPath;
}

void Bluetooth1Adaptor::SetAdapterAlias(const QDBusObjectPath &adapter, const QString &alias)
{
    // handle method call org.deepin.dde.Bluetooth1.SetAdapterAlias
    QMetaObject::invokeMethod(parent(), "SetAdapterAlias", Q_ARG(QDBusObjectPath, adapter), Q_ARG(QString, alias));
}

void Bluetooth1Adaptor::SetAdapterDiscoverable(const QDBusObjectPath &adapter, bool discoverable)
{
    // handle method call org.deepin.dde.Bluetooth1.SetAdapterDiscoverable
    QMetaObject::invokeMethod(parent(), "SetAdapterDiscoverable", Q_ARG(QDBusObjectPath, adapter), Q_ARG(bool, discoverable));
}

void Bluetooth1Adaptor::SetAdapterDiscoverableTimeout(const QDBusObjectPath &adapter, uint timeout)
{
    // handle method call org.deepin.dde.Bluetooth1.SetAdapterDiscoverableTimeout
    QMetaObject::invokeMethod(parent(), "SetAdapterDiscoverableTimeout", Q_ARG(QDBusObjectPath, adapter), Q_ARG(uint, timeout));
}

void Bluetooth1Adaptor::SetAdapterDiscovering(const QDBusObjectPath &adapter, bool discovering)
{
    // handle method call org.deepin.dde.Bluetooth1.SetAdapterDiscovering
    QMetaObject::invokeMethod(parent(), "SetAdapterDiscovering", Q_ARG(QDBusObjectPath, adapter), Q_ARG(bool, discovering));
}

void Bluetooth1Adaptor::SetAdapterPowered(const QDBusObjectPath &adapter, bool powered)
{
    // handle method call org.deepin.dde.Bluetooth1.SetAdapterPowered
    QMetaObject::invokeMethod(parent(), "SetAdapterPowered", Q_ARG(QDBusObjectPath, adapter), Q_ARG(bool, powered));
}

void Bluetooth1Adaptor::SetDeviceAlias(const QDBusObjectPath &device, const QString &alias)
{
    // handle method call org.deepin.dde.Bluetooth1.SetDeviceAlias
    QMetaObject::invokeMethod(parent(), "SetDeviceAlias", Q_ARG(QDBusObjectPath, device), Q_ARG(QString, alias));
}

void Bluetooth1Adaptor::SetDeviceTrusted(const QDBusObjectPath &device, bool trusted)
{
    // handle method call org.deepin.dde.Bluetooth1.SetDeviceTrusted
    QMetaObject::invokeMethod(parent(), "SetDeviceTrusted", Q_ARG(QDBusObjectPath, device), Q_ARG(bool, trusted));
}

