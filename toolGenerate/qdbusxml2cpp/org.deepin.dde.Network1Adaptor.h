/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.deepin.dde.Network1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Network1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Network1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DEEPIN_DDE_NETWORK1ADAPTOR_H
#define ORG_DEEPIN_DDE_NETWORK1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Network1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.deepin.dde.Network1
 */
class Network1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.dde.Network1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.dde.Network1\">\n"
"    <method name=\"ActivateAccessPoint\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"out\" type=\"o\"/>\n"
"    </method>\n"
"    <method name=\"ActivateConnection\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"out\" type=\"o\"/>\n"
"    </method>\n"
"    <method name=\"CancelSecret\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"CreateConnection\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"out\" type=\"o\"/>\n"
"    </method>\n"
"    <method name=\"CreateConnectionForAccessPoint\">\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"out\" type=\"o\"/>\n"
"    </method>\n"
"    <method name=\"DeactivateConnection\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"DeleteConnection\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"DisableWirelessHotspotMode\">\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"    </method>\n"
"    <method name=\"DisconnectDevice\">\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"    </method>\n"
"    <method name=\"EditConnection\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"out\" type=\"o\"/>\n"
"    </method>\n"
"    <method name=\"EnableDevice\">\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"in\" type=\"b\"/>\n"
"    </method>\n"
"    <method name=\"EnableWirelessHotspotMode\">\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"    </method>\n"
"    <method name=\"ListDeviceConnections\">\n"
"      <arg direction=\"out\" type=\"ao\"/>\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"    </method>\n"
"    <method name=\"FeedSecret\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"b\"/>\n"
"    </method>\n"
"    <method name=\"GetAccessPoints\">\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"GetActiveConnectionInfo\">\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"GetAutoProxy\">\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"GetProxy\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"GetProxyIgnoreHosts\">\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"GetProxyMethod\">\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"GetSupportedConnectionTypes\">\n"
"      <arg direction=\"out\" type=\"as\"/>\n"
"    </method>\n"
"    <method name=\"GetWiredConnectionUuid\">\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"IsDeviceEnabled\">\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"out\" type=\"b\"/>\n"
"    </method>\n"
"    <method name=\"IsPasswordValid\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"out\" type=\"b\"/>\n"
"    </method>\n"
"    <method name=\"IsWirelessHotspotModeEnabled\">\n"
"      <arg direction=\"in\" type=\"o\"/>\n"
"      <arg direction=\"out\" type=\"b\"/>\n"
"    </method>\n"
"    <method name=\"RegisterSecretReceiver\"/>\n"
"    <method name=\"RequestWirelessScan\"/>\n"
"    <method name=\"SetAutoProxy\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"SetDeviceManaged\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"b\"/>\n"
"    </method>\n"
"    <method name=\"SetProxy\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"SetProxyIgnoreHosts\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"SetProxyMethod\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"RequestIPConflictCheck\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <signal name=\"NeedSecrets\">\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"NeedSecretsFinished\">\n"
"      <arg type=\"s\"/>\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"AccessPointAdded\">\n"
"      <arg type=\"s\"/>\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"AccessPointRemoved\">\n"
"      <arg type=\"s\"/>\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"AccessPointPropertiesChanged\">\n"
"      <arg type=\"s\"/>\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"DeviceEnabled\">\n"
"      <arg type=\"s\"/>\n"
"      <arg type=\"b\"/>\n"
"    </signal>\n"
"    <signal name=\"ActiveConnectionInfoChanged\"/>\n"
"    <signal name=\"IPConflict\">\n"
"      <arg type=\"s\"/>\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <property access=\"read\" type=\"u\" name=\"State\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"WirelessAccessPoints\"/>\n"
"    <property access=\"read\" type=\"u\" name=\"Connectivity\"/>\n"
"    <property access=\"readwrite\" type=\"b\" name=\"NetworkingEnabled\"/>\n"
"    <property access=\"readwrite\" type=\"b\" name=\"VpnEnabled\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"Devices\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"Connections\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"ActiveConnections\"/>\n"
"  </interface>\n"
        "")
public:
    Network1Adaptor(QObject *parent);
    virtual ~Network1Adaptor();

public: // PROPERTIES
    Q_PROPERTY(QString ActiveConnections READ activeConnections)
    QString activeConnections() const;

    Q_PROPERTY(QString Connections READ connections)
    QString connections() const;

    Q_PROPERTY(uint Connectivity READ connectivity)
    uint connectivity() const;

    Q_PROPERTY(QString Devices READ devices)
    QString devices() const;

    Q_PROPERTY(bool NetworkingEnabled READ networkingEnabled WRITE setNetworkingEnabled)
    bool networkingEnabled() const;
    void setNetworkingEnabled(bool value);

    Q_PROPERTY(uint State READ state)
    uint state() const;

    Q_PROPERTY(bool VpnEnabled READ vpnEnabled WRITE setVpnEnabled)
    bool vpnEnabled() const;
    void setVpnEnabled(bool value);

    Q_PROPERTY(QString WirelessAccessPoints READ wirelessAccessPoints)
    QString wirelessAccessPoints() const;

public Q_SLOTS: // METHODS
    QDBusObjectPath ActivateAccessPoint(const QString &in0, const QDBusObjectPath &in1, const QDBusObjectPath &in2);
    QDBusObjectPath ActivateConnection(const QString &in0, const QDBusObjectPath &in1);
    void CancelSecret(const QString &in0, const QString &in1);
    QDBusObjectPath CreateConnection(const QString &in0, const QDBusObjectPath &in1);
    QDBusObjectPath CreateConnectionForAccessPoint(const QDBusObjectPath &in0, const QDBusObjectPath &in1);
    void DeactivateConnection(const QString &in0);
    void DeleteConnection(const QString &in0);
    void DisableWirelessHotspotMode(const QDBusObjectPath &in0);
    void DisconnectDevice(const QDBusObjectPath &in0);
    QDBusObjectPath EditConnection(const QString &in0, const QDBusObjectPath &in1);
    void EnableDevice(const QDBusObjectPath &in0, bool in1);
    void EnableWirelessHotspotMode(const QDBusObjectPath &in0);
    void FeedSecret(const QString &in0, const QString &in1, const QString &in2, bool in3);
    QString GetAccessPoints(const QDBusObjectPath &in0);
    QString GetActiveConnectionInfo();
    QString GetAutoProxy();
    QString GetProxy(const QString &in0, QString &out1);
    QString GetProxyIgnoreHosts();
    QString GetProxyMethod();
    QStringList GetSupportedConnectionTypes();
    QString GetWiredConnectionUuid(const QDBusObjectPath &in0);
    bool IsDeviceEnabled(const QDBusObjectPath &in0);
    bool IsPasswordValid(const QString &in0, const QString &in1);
    bool IsWirelessHotspotModeEnabled(const QDBusObjectPath &in0);
    QList<QDBusObjectPath> ListDeviceConnections(const QDBusObjectPath &in0);
    void RegisterSecretReceiver();
    void RequestIPConflictCheck(const QString &in0, const QString &in1);
    void RequestWirelessScan();
    void SetAutoProxy(const QString &in0);
    void SetDeviceManaged(const QString &in0, bool in1);
    void SetProxy(const QString &in0, const QString &in1, const QString &in2);
    void SetProxyIgnoreHosts(const QString &in0);
    void SetProxyMethod(const QString &in0);
Q_SIGNALS: // SIGNALS
    void AccessPointAdded(const QString &in0, const QString &in1);
    void AccessPointPropertiesChanged(const QString &in0, const QString &in1);
    void AccessPointRemoved(const QString &in0, const QString &in1);
    void ActiveConnectionInfoChanged();
    void DeviceEnabled(const QString &in0, bool in1);
    void IPConflict(const QString &in0, const QString &in1);
    void NeedSecrets(const QString &in0);
    void NeedSecretsFinished(const QString &in0, const QString &in1);
};

#endif
