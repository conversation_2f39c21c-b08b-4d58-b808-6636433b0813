/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/application-tray/api/dbus/org.kde.StatusNotifierItem.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.kde.StatusNotifierItemAdaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.kde.StatusNotifierItem.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_KDE_STATUSNOTIFIERITEMADAPTOR_H
#define ORG_KDE_STATUSNOTIFIERITEMADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.kde.StatusNotifierItem.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.kde.StatusNotifierItem
 */
class StatusNotifierItemAdaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.kde.StatusNotifierItem")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.kde.StatusNotifierItem\">\n"
"    <property access=\"read\" type=\"s\" name=\"Category\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"Id\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"Title\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"Status\"/>\n"
"    <property access=\"read\" type=\"i\" name=\"WindowId\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"IconThemePath\"/>\n"
"    <property access=\"read\" type=\"o\" name=\"Menu\"/>\n"
"    <property access=\"read\" type=\"b\" name=\"ItemIsMenu\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"IconName\"/>\n"
"    <property access=\"read\" type=\"a(iiay)\" name=\"IconPixmap\">\n"
"      <annotation value=\"DBusImageList\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"s\" name=\"OverlayIconName\"/>\n"
"    <property access=\"read\" type=\"a(iiay)\" name=\"OverlayIconPixmap\">\n"
"      <annotation value=\"DBusImageList\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"s\" name=\"AttentionIconName\"/>\n"
"    <property access=\"read\" type=\"a(iiay)\" name=\"AttentionIconPixmap\">\n"
"      <annotation value=\"DBusImageList\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"s\" name=\"AttentionMovieName\"/>\n"
"    <property access=\"read\" type=\"(sa(iiay)ss)\" name=\"ToolTip\">\n"
"      <annotation value=\"DBusToolTip\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"    <method name=\"ContextMenu\">\n"
"      <arg direction=\"in\" type=\"i\" name=\"x\"/>\n"
"      <arg direction=\"in\" type=\"i\" name=\"y\"/>\n"
"    </method>\n"
"    <method name=\"Activate\">\n"
"      <arg direction=\"in\" type=\"i\" name=\"x\"/>\n"
"      <arg direction=\"in\" type=\"i\" name=\"y\"/>\n"
"    </method>\n"
"    <method name=\"SecondaryActivate\">\n"
"      <arg direction=\"in\" type=\"i\" name=\"x\"/>\n"
"      <arg direction=\"in\" type=\"i\" name=\"y\"/>\n"
"    </method>\n"
"    <method name=\"Scroll\">\n"
"      <arg direction=\"in\" type=\"i\" name=\"delta\"/>\n"
"      <arg direction=\"in\" type=\"s\" name=\"orientation\"/>\n"
"    </method>\n"
"    <signal name=\"NewTitle\"/>\n"
"    <signal name=\"NewIcon\"/>\n"
"    <signal name=\"NewAttentionIcon\"/>\n"
"    <signal name=\"NewOverlayIcon\"/>\n"
"    <signal name=\"NewToolTip\"/>\n"
"    <signal name=\"NewStatus\">\n"
"      <arg type=\"s\" name=\"status\"/>\n"
"    </signal>\n"
"  </interface>\n"
        "")
public:
    StatusNotifierItemAdaptor(QObject *parent);
    virtual ~StatusNotifierItemAdaptor();

public: // PROPERTIES
    Q_PROPERTY(QString AttentionIconName READ attentionIconName)
    QString attentionIconName() const;

    Q_PROPERTY(DBusImageList AttentionIconPixmap READ attentionIconPixmap)
    DBusImageList attentionIconPixmap() const;

    Q_PROPERTY(QString AttentionMovieName READ attentionMovieName)
    QString attentionMovieName() const;

    Q_PROPERTY(QString Category READ category)
    QString category() const;

    Q_PROPERTY(QString IconName READ iconName)
    QString iconName() const;

    Q_PROPERTY(DBusImageList IconPixmap READ iconPixmap)
    DBusImageList iconPixmap() const;

    Q_PROPERTY(QString IconThemePath READ iconThemePath)
    QString iconThemePath() const;

    Q_PROPERTY(QString Id READ id)
    QString id() const;

    Q_PROPERTY(bool ItemIsMenu READ itemIsMenu)
    bool itemIsMenu() const;

    Q_PROPERTY(QDBusObjectPath Menu READ menu)
    QDBusObjectPath menu() const;

    Q_PROPERTY(QString OverlayIconName READ overlayIconName)
    QString overlayIconName() const;

    Q_PROPERTY(DBusImageList OverlayIconPixmap READ overlayIconPixmap)
    DBusImageList overlayIconPixmap() const;

    Q_PROPERTY(QString Status READ status)
    QString status() const;

    Q_PROPERTY(QString Title READ title)
    QString title() const;

    Q_PROPERTY(DBusToolTip ToolTip READ toolTip)
    DBusToolTip toolTip() const;

    Q_PROPERTY(int WindowId READ windowId)
    int windowId() const;

public Q_SLOTS: // METHODS
    void Activate(int x, int y);
    void ContextMenu(int x, int y);
    void Scroll(int delta, const QString &orientation);
    void SecondaryActivate(int x, int y);
Q_SIGNALS: // SIGNALS
    void NewAttentionIcon();
    void NewIcon();
    void NewOverlayIcon();
    void NewStatus(const QString &status);
    void NewTitle();
    void NewToolTip();
};

#endif
