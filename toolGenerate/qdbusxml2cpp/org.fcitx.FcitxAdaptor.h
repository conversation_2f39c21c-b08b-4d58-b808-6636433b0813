/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/keyboard-layout/org.fcitx.Fcitx.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.fcitx.FcitxAdaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.fcitx.Fcitx.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_FCITX_FCITXADAPTOR_H
#define ORG_FCITX_FCITXADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.fcitx.Fcitx.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.fcitx.Fcitx.InputMethod
 */
class InputMethodAdaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.fcitx.Fcitx.InputMethod")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.fcitx.Fcitx.InputMethod\">\n"
"    <method name=\"CreateIC\">\n"
"      <arg direction=\"out\" type=\"i\" name=\"icid\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"keyval1\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"state1\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"keyval2\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"state2\"/>\n"
"    </method>\n"
"    <method name=\"CreateICv2\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"appname\"/>\n"
"      <arg direction=\"out\" type=\"i\" name=\"icid\"/>\n"
"      <arg direction=\"out\" type=\"b\" name=\"enable\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"keyval1\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"state1\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"keyval2\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"state2\"/>\n"
"    </method>\n"
"    <method name=\"CreateICv3\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"appname\"/>\n"
"      <arg direction=\"in\" type=\"i\" name=\"pid\"/>\n"
"      <arg direction=\"out\" type=\"i\" name=\"icid\"/>\n"
"      <arg direction=\"out\" type=\"b\" name=\"enable\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"keyval1\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"state1\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"keyval2\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"state2\"/>\n"
"    </method>\n"
"    <method name=\"Exit\"/>\n"
"    <method name=\"GetCurrentIM\">\n"
"      <arg direction=\"out\" type=\"s\" name=\"im\"/>\n"
"    </method>\n"
"    <method name=\"GetIMByIndex\">\n"
"      <arg direction=\"in\" type=\"i\" name=\"index\"/>\n"
"      <arg direction=\"out\" type=\"s\" name=\"im\"/>\n"
"    </method>\n"
"    <method name=\"SetCurrentIM\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"im\"/>\n"
"    </method>\n"
"    <method name=\"ReloadConfig\"/>\n"
"    <method name=\"ReloadAddonConfig\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"addon\"/>\n"
"    </method>\n"
"    <method name=\"Restart\"/>\n"
"    <method name=\"Configure\"/>\n"
"    <method name=\"ConfigureAddon\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"addon\"/>\n"
"    </method>\n"
"    <method name=\"ConfigureIM\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"im\"/>\n"
"    </method>\n"
"    <method name=\"GetCurrentUI\">\n"
"      <arg direction=\"out\" type=\"s\" name=\"addon\"/>\n"
"    </method>\n"
"    <method name=\"GetIMAddon\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"im\"/>\n"
"      <arg direction=\"out\" type=\"s\" name=\"addon\"/>\n"
"    </method>\n"
"    <method name=\"ActivateIM\"/>\n"
"    <method name=\"InactivateIM\"/>\n"
"    <method name=\"ToggleIM\"/>\n"
"    <method name=\"SwitchIM\"/>\n"
"    <method name=\"ResetIMList\"/>\n"
"    <method name=\"GetCurrentState\">\n"
"      <arg direction=\"out\" type=\"i\" name=\"state\"/>\n"
"    </method>\n"
"    <signal name=\"ReloadConfigUI\"/>\n"
"    <property access=\"readwrite\" type=\"a(sssb)\" name=\"IMList\">\n"
"      <annotation value=\"FcitxQtInputMethodItemList\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"      <annotation value=\"true\" name=\"org.freedesktop.DBus.Property.EmitsChangedSignal\"/>\n"
"    </property>\n"
"    <property access=\"readwrite\" type=\"s\" name=\"CurrentIM\">\n"
"      <annotation value=\"true\" name=\"org.freedesktop.DBus.Property.EmitsChangedSignal\"/>\n"
"    </property>\n"
"  </interface>\n"
        "")
public:
    InputMethodAdaptor(QObject *parent);
    virtual ~InputMethodAdaptor();

public: // PROPERTIES
    Q_PROPERTY(QString CurrentIM READ currentIM WRITE setCurrentIM)
    QString currentIM() const;
    void setCurrentIM(const QString &value);

    Q_PROPERTY(FcitxQtInputMethodItemList IMList READ iMList WRITE setIMList)
    FcitxQtInputMethodItemList iMList() const;
    void setIMList(FcitxQtInputMethodItemList value);

public Q_SLOTS: // METHODS
    void ActivateIM();
    void Configure();
    void ConfigureAddon(const QString &addon);
    void ConfigureIM(const QString &im);
    int CreateIC(uint &keyval1, uint &state1, uint &keyval2, uint &state2);
    int CreateICv2(const QString &appname, bool &enable, uint &keyval1, uint &state1, uint &keyval2, uint &state2);
    int CreateICv3(const QString &appname, int pid, bool &enable, uint &keyval1, uint &state1, uint &keyval2, uint &state2);
    void Exit();
    QString GetCurrentIM();
    int GetCurrentState();
    QString GetCurrentUI();
    QString GetIMAddon(const QString &im);
    QString GetIMByIndex(int index);
    void InactivateIM();
    void ReloadAddonConfig(const QString &addon);
    void ReloadConfig();
    void ResetIMList();
    void Restart();
    void SetCurrentIM(const QString &im);
    void SwitchIM();
    void ToggleIM();
Q_SIGNALS: // SIGNALS
    void ReloadConfigUI();
};

#endif
