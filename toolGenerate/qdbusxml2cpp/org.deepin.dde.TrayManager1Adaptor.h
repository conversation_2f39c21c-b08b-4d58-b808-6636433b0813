/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/application-tray/api/dbus/org.deepin.dde.TrayManager1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.TrayManager1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.TrayManager1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DEEPIN_DDE_TRAYMANAGER1ADAPTOR_H
#define ORG_DEEPIN_DDE_TRAYMANAGER1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.TrayManager1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.deepin.dde.TrayManager1
 */
class TrayManager1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.dde.TrayManager1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.dde.TrayManager1\">\n"
"    <method name=\"EnableNotification\">\n"
"      <arg direction=\"in\" type=\"u\" name=\"win\"/>\n"
"      <arg direction=\"in\" type=\"b\" name=\"enabled\"/>\n"
"    </method>\n"
"    <method name=\"GetName\">\n"
"      <arg direction=\"in\" type=\"u\" name=\"win\"/>\n"
"      <arg direction=\"out\" type=\"s\" name=\"name\"/>\n"
"    </method>\n"
"    <method name=\"Manage\">\n"
"      <arg direction=\"out\" type=\"b\" name=\"ok\"/>\n"
"    </method>\n"
"    <signal name=\"Inited\"/>\n"
"    <signal name=\"Added\">\n"
"      <arg type=\"u\" name=\"id\"/>\n"
"    </signal>\n"
"    <signal name=\"Removed\">\n"
"      <arg type=\"u\" name=\"id\"/>\n"
"    </signal>\n"
"    <signal name=\"Changed\">\n"
"      <arg type=\"u\" name=\"id\"/>\n"
"    </signal>\n"
"    <property access=\"read\" type=\"au\" name=\"TrayIcons\">\n"
"      <annotation value=\"TrayList\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"  </interface>\n"
        "")
public:
    TrayManager1Adaptor(QObject *parent);
    virtual ~TrayManager1Adaptor();

public: // PROPERTIES
    Q_PROPERTY(TrayList TrayIcons READ trayIcons)
    TrayList trayIcons() const;

public Q_SLOTS: // METHODS
    void EnableNotification(uint win, bool enabled);
    QString GetName(uint win);
    bool Manage();
Q_SIGNALS: // SIGNALS
    void Added(uint id);
    void Changed(uint id);
    void Inited();
    void Removed(uint id);
};

#endif
