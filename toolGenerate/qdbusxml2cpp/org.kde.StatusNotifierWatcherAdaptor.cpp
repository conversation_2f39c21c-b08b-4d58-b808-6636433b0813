/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/application-tray/api/dbus/org.kde.StatusNotifierWatcher.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.kde.StatusNotifierWatcherAdaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.kde.StatusNotifierWatcher.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.kde.StatusNotifierWatcherAdaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class StatusNotifierWatcherAdaptor
 */

StatusNotifierWatcherAdaptor::StatusNotifierWatcherAdaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

StatusNotifierWatcherAdaptor::~StatusNotifierWatcherAdaptor()
{
    // destructor
}

bool StatusNotifierWatcherAdaptor::isStatusNotifierHostRegistered() const
{
    // get the value of property IsStatusNotifierHostRegistered
    return qvariant_cast< bool >(parent()->property("IsStatusNotifierHostRegistered"));
}

int StatusNotifierWatcherAdaptor::protocolVersion() const
{
    // get the value of property ProtocolVersion
    return qvariant_cast< int >(parent()->property("ProtocolVersion"));
}

QStringList StatusNotifierWatcherAdaptor::registeredStatusNotifierItems() const
{
    // get the value of property RegisteredStatusNotifierItems
    return qvariant_cast< QStringList >(parent()->property("RegisteredStatusNotifierItems"));
}

void StatusNotifierWatcherAdaptor::RegisterStatusNotifierHost(const QString &serviceName)
{
    // handle method call org.kde.StatusNotifierWatcher.RegisterStatusNotifierHost
    QMetaObject::invokeMethod(parent(), "RegisterStatusNotifierHost", Q_ARG(QString, serviceName));
}

void StatusNotifierWatcherAdaptor::RegisterStatusNotifierItem(const QString &serviceOrPath)
{
    // handle method call org.kde.StatusNotifierWatcher.RegisterStatusNotifierItem
    QMetaObject::invokeMethod(parent(), "RegisterStatusNotifierItem", Q_ARG(QString, serviceOrPath));
}

