/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/libdbusmenuqt/com.canonical.dbusmenu.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/com.canonical.dbusmenuAdaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/com.canonical.dbusmenu.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/com.canonical.dbusmenuAdaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class DbusmenuAdaptor
 */

DbusmenuAdaptor::DbusmenuAdaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

DbusmenuAdaptor::~DbusmenuAdaptor()
{
    // destructor
}

QString DbusmenuAdaptor::status() const
{
    // get the value of property Status
    return qvariant_cast< QString >(parent()->property("Status"));
}

uint DbusmenuAdaptor::version() const
{
    // get the value of property Version
    return qvariant_cast< uint >(parent()->property("Version"));
}

bool DbusmenuAdaptor::AboutToShow(int id)
{
    // handle method call com.canonical.dbusmenu.AboutToShow
    bool out0;
    QMetaObject::invokeMethod(parent(), "AboutToShow", Q_RETURN_ARG(bool, out0), Q_ARG(int, id));
    return out0;
}

void DbusmenuAdaptor::Event(int id, const QString &eventId, const QDBusVariant &data, uint timestamp)
{
    // handle method call com.canonical.dbusmenu.Event
    QMetaObject::invokeMethod(parent(), "Event", Q_ARG(int, id), Q_ARG(QString, eventId), Q_ARG(QDBusVariant, data), Q_ARG(uint, timestamp));
}

DBusMenuItemList DbusmenuAdaptor::GetGroupProperties(const QList<int> &ids, const QStringList &propertyNames)
{
    // handle method call com.canonical.dbusmenu.GetGroupProperties
    DBusMenuItemList out0;
    QMetaObject::invokeMethod(parent(), "GetGroupProperties", Q_RETURN_ARG(DBusMenuItemList, out0), Q_ARG(QList<int>, ids), Q_ARG(QStringList, propertyNames));
    return out0;
}

uint DbusmenuAdaptor::GetLayout(int parentId, int recursionDepth, const QStringList &propertyNames, DBusMenuLayoutItem &item)
{
    // handle method call com.canonical.dbusmenu.GetLayout
    //return static_cast<YourObjectType *>(parent())->GetLayout(parentId, recursionDepth, propertyNames, item);
}

QDBusVariant DbusmenuAdaptor::GetProperty(int id, const QString &property)
{
    // handle method call com.canonical.dbusmenu.GetProperty
    QDBusVariant out0;
    QMetaObject::invokeMethod(parent(), "GetProperty", Q_RETURN_ARG(QDBusVariant, out0), Q_ARG(int, id), Q_ARG(QString, property));
    return out0;
}

