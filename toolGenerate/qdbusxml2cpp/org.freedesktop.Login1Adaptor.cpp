/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.freedesktop.Login1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.freedesktop.Login1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.freedesktop.Login1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.freedesktop.Login1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class ManagerAdaptor
 */

ManagerAdaptor::ManagerAdaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

ManagerAdaptor::~ManagerAdaptor()
{
    // destructor
}

QString ManagerAdaptor::blockInhibited() const
{
    // get the value of property BlockInhibited
    return qvariant_cast< QString >(parent()->property("BlockInhibited"));
}

QString ManagerAdaptor::delayInhibited() const
{
    // get the value of property DelayInhibited
    return qvariant_cast< QString >(parent()->property("DelayInhibited"));
}

bool ManagerAdaptor::docked() const
{
    // get the value of property Docked
    return qvariant_cast< bool >(parent()->property("Docked"));
}

bool ManagerAdaptor::enableWallMessages() const
{
    // get the value of property EnableWallMessages
    return qvariant_cast< bool >(parent()->property("EnableWallMessages"));
}

void ManagerAdaptor::setEnableWallMessages(bool value)
{
    // set the value of property EnableWallMessages
    parent()->setProperty("EnableWallMessages", QVariant::fromValue(value));
}

QString ManagerAdaptor::handleHibernateKey() const
{
    // get the value of property HandleHibernateKey
    return qvariant_cast< QString >(parent()->property("HandleHibernateKey"));
}

QString ManagerAdaptor::handleLidSwitch() const
{
    // get the value of property HandleLidSwitch
    return qvariant_cast< QString >(parent()->property("HandleLidSwitch"));
}

QString ManagerAdaptor::handleLidSwitchDocked() const
{
    // get the value of property HandleLidSwitchDocked
    return qvariant_cast< QString >(parent()->property("HandleLidSwitchDocked"));
}

QString ManagerAdaptor::handlePowerKey() const
{
    // get the value of property HandlePowerKey
    return qvariant_cast< QString >(parent()->property("HandlePowerKey"));
}

QString ManagerAdaptor::handleSuspendKey() const
{
    // get the value of property HandleSuspendKey
    return qvariant_cast< QString >(parent()->property("HandleSuspendKey"));
}

qulonglong ManagerAdaptor::holdoffTimeoutUSec() const
{
    // get the value of property HoldoffTimeoutUSec
    return qvariant_cast< qulonglong >(parent()->property("HoldoffTimeoutUSec"));
}

QString ManagerAdaptor::idleAction() const
{
    // get the value of property IdleAction
    return qvariant_cast< QString >(parent()->property("IdleAction"));
}

qulonglong ManagerAdaptor::idleActionUSec() const
{
    // get the value of property IdleActionUSec
    return qvariant_cast< qulonglong >(parent()->property("IdleActionUSec"));
}

bool ManagerAdaptor::idleHint() const
{
    // get the value of property IdleHint
    return qvariant_cast< bool >(parent()->property("IdleHint"));
}

qulonglong ManagerAdaptor::idleSinceHint() const
{
    // get the value of property IdleSinceHint
    return qvariant_cast< qulonglong >(parent()->property("IdleSinceHint"));
}

qulonglong ManagerAdaptor::idleSinceHintMonotonic() const
{
    // get the value of property IdleSinceHintMonotonic
    return qvariant_cast< qulonglong >(parent()->property("IdleSinceHintMonotonic"));
}

qulonglong ManagerAdaptor::inhibitDelayMaxUSec() const
{
    // get the value of property InhibitDelayMaxUSec
    return qvariant_cast< qulonglong >(parent()->property("InhibitDelayMaxUSec"));
}

qulonglong ManagerAdaptor::inhibitorsMax() const
{
    // get the value of property InhibitorsMax
    return qvariant_cast< qulonglong >(parent()->property("InhibitorsMax"));
}

QStringList ManagerAdaptor::killExcludeUsers() const
{
    // get the value of property KillExcludeUsers
    return qvariant_cast< QStringList >(parent()->property("KillExcludeUsers"));
}

QStringList ManagerAdaptor::killOnlyUsers() const
{
    // get the value of property KillOnlyUsers
    return qvariant_cast< QStringList >(parent()->property("KillOnlyUsers"));
}

bool ManagerAdaptor::killUserProcesses() const
{
    // get the value of property KillUserProcesses
    return qvariant_cast< bool >(parent()->property("KillUserProcesses"));
}

uint ManagerAdaptor::nAutoVTs() const
{
    // get the value of property NAutoVTs
    return qvariant_cast< uint >(parent()->property("NAutoVTs"));
}

qulonglong ManagerAdaptor::nCurrentInhibitors() const
{
    // get the value of property NCurrentInhibitors
    return qvariant_cast< qulonglong >(parent()->property("NCurrentInhibitors"));
}

qulonglong ManagerAdaptor::nCurrentSessions() const
{
    // get the value of property NCurrentSessions
    return qvariant_cast< qulonglong >(parent()->property("NCurrentSessions"));
}

bool ManagerAdaptor::preparingForShutdown() const
{
    // get the value of property PreparingForShutdown
    return qvariant_cast< bool >(parent()->property("PreparingForShutdown"));
}

bool ManagerAdaptor::preparingForSleep() const
{
    // get the value of property PreparingForSleep
    return qvariant_cast< bool >(parent()->property("PreparingForSleep"));
}

bool ManagerAdaptor::rebootToFirmwareSetup() const
{
    // get the value of property RebootToFirmwareSetup
    return qvariant_cast< bool >(parent()->property("RebootToFirmwareSetup"));
}

bool ManagerAdaptor::removeIPC() const
{
    // get the value of property RemoveIPC
    return qvariant_cast< bool >(parent()->property("RemoveIPC"));
}

qulonglong ManagerAdaptor::runtimeDirectorySize() const
{
    // get the value of property RuntimeDirectorySize
    return qvariant_cast< qulonglong >(parent()->property("RuntimeDirectorySize"));
}

qulonglong ManagerAdaptor::sessionsMax() const
{
    // get the value of property SessionsMax
    return qvariant_cast< qulonglong >(parent()->property("SessionsMax"));
}

qulonglong ManagerAdaptor::userTasksMax() const
{
    // get the value of property UserTasksMax
    return qvariant_cast< qulonglong >(parent()->property("UserTasksMax"));
}

QString ManagerAdaptor::wallMessage() const
{
    // get the value of property WallMessage
    return qvariant_cast< QString >(parent()->property("WallMessage"));
}

void ManagerAdaptor::setWallMessage(const QString &value)
{
    // set the value of property WallMessage
    parent()->setProperty("WallMessage", QVariant::fromValue(value));
}

void ManagerAdaptor::ActivateSession(const QString &in0)
{
    // handle method call org.freedesktop.login1.Manager.ActivateSession
    QMetaObject::invokeMethod(parent(), "ActivateSession", Q_ARG(QString, in0));
}

void ManagerAdaptor::ActivateSessionOnSeat(const QString &in0, const QString &in1)
{
    // handle method call org.freedesktop.login1.Manager.ActivateSessionOnSeat
    QMetaObject::invokeMethod(parent(), "ActivateSessionOnSeat", Q_ARG(QString, in0), Q_ARG(QString, in1));
}

void ManagerAdaptor::AttachDevice(const QString &in0, const QString &in1, bool in2)
{
    // handle method call org.freedesktop.login1.Manager.AttachDevice
    QMetaObject::invokeMethod(parent(), "AttachDevice", Q_ARG(QString, in0), Q_ARG(QString, in1), Q_ARG(bool, in2));
}

QString ManagerAdaptor::CanHibernate()
{
    // handle method call org.freedesktop.login1.Manager.CanHibernate
    QString out0;
    QMetaObject::invokeMethod(parent(), "CanHibernate", Q_RETURN_ARG(QString, out0));
    return out0;
}

QString ManagerAdaptor::CanHybridSleep()
{
    // handle method call org.freedesktop.login1.Manager.CanHybridSleep
    QString out0;
    QMetaObject::invokeMethod(parent(), "CanHybridSleep", Q_RETURN_ARG(QString, out0));
    return out0;
}

QString ManagerAdaptor::CanPowerOff()
{
    // handle method call org.freedesktop.login1.Manager.CanPowerOff
    QString out0;
    QMetaObject::invokeMethod(parent(), "CanPowerOff", Q_RETURN_ARG(QString, out0));
    return out0;
}

QString ManagerAdaptor::CanReboot()
{
    // handle method call org.freedesktop.login1.Manager.CanReboot
    QString out0;
    QMetaObject::invokeMethod(parent(), "CanReboot", Q_RETURN_ARG(QString, out0));
    return out0;
}

QString ManagerAdaptor::CanRebootToFirmwareSetup()
{
    // handle method call org.freedesktop.login1.Manager.CanRebootToFirmwareSetup
    QString out0;
    QMetaObject::invokeMethod(parent(), "CanRebootToFirmwareSetup", Q_RETURN_ARG(QString, out0));
    return out0;
}

QString ManagerAdaptor::CanSuspend()
{
    // handle method call org.freedesktop.login1.Manager.CanSuspend
    QString out0;
    QMetaObject::invokeMethod(parent(), "CanSuspend", Q_RETURN_ARG(QString, out0));
    return out0;
}

bool ManagerAdaptor::CancelScheduledShutdown()
{
    // handle method call org.freedesktop.login1.Manager.CancelScheduledShutdown
    bool out0;
    QMetaObject::invokeMethod(parent(), "CancelScheduledShutdown", Q_RETURN_ARG(bool, out0));
    return out0;
}

void ManagerAdaptor::FlushDevices(bool in0)
{
    // handle method call org.freedesktop.login1.Manager.FlushDevices
    QMetaObject::invokeMethod(parent(), "FlushDevices", Q_ARG(bool, in0));
}

QDBusObjectPath ManagerAdaptor::GetSeat(const QString &in0)
{
    // handle method call org.freedesktop.login1.Manager.GetSeat
    QDBusObjectPath out0;
    QMetaObject::invokeMethod(parent(), "GetSeat", Q_RETURN_ARG(QDBusObjectPath, out0), Q_ARG(QString, in0));
    return out0;
}

QDBusObjectPath ManagerAdaptor::GetSession(const QString &in0)
{
    // handle method call org.freedesktop.login1.Manager.GetSession
    QDBusObjectPath out0;
    QMetaObject::invokeMethod(parent(), "GetSession", Q_RETURN_ARG(QDBusObjectPath, out0), Q_ARG(QString, in0));
    return out0;
}

QDBusObjectPath ManagerAdaptor::GetSessionByPID(uint in0)
{
    // handle method call org.freedesktop.login1.Manager.GetSessionByPID
    QDBusObjectPath out0;
    QMetaObject::invokeMethod(parent(), "GetSessionByPID", Q_RETURN_ARG(QDBusObjectPath, out0), Q_ARG(uint, in0));
    return out0;
}

QDBusObjectPath ManagerAdaptor::GetUser(uint in0)
{
    // handle method call org.freedesktop.login1.Manager.GetUser
    QDBusObjectPath out0;
    QMetaObject::invokeMethod(parent(), "GetUser", Q_RETURN_ARG(QDBusObjectPath, out0), Q_ARG(uint, in0));
    return out0;
}

QDBusObjectPath ManagerAdaptor::GetUserByPID(uint in0)
{
    // handle method call org.freedesktop.login1.Manager.GetUserByPID
    QDBusObjectPath out0;
    QMetaObject::invokeMethod(parent(), "GetUserByPID", Q_RETURN_ARG(QDBusObjectPath, out0), Q_ARG(uint, in0));
    return out0;
}

void ManagerAdaptor::Hibernate(bool in0)
{
    // handle method call org.freedesktop.login1.Manager.Hibernate
    QMetaObject::invokeMethod(parent(), "Hibernate", Q_ARG(bool, in0));
}

void ManagerAdaptor::HybridSleep(bool in0)
{
    // handle method call org.freedesktop.login1.Manager.HybridSleep
    QMetaObject::invokeMethod(parent(), "HybridSleep", Q_ARG(bool, in0));
}

QDBusUnixFileDescriptor ManagerAdaptor::Inhibit(const QString &in0, const QString &in1, const QString &in2, const QString &in3)
{
    // handle method call org.freedesktop.login1.Manager.Inhibit
    QDBusUnixFileDescriptor out0;
    QMetaObject::invokeMethod(parent(), "Inhibit", Q_RETURN_ARG(QDBusUnixFileDescriptor, out0), Q_ARG(QString, in0), Q_ARG(QString, in1), Q_ARG(QString, in2), Q_ARG(QString, in3));
    return out0;
}

void ManagerAdaptor::KillSession(const QString &in0, const QString &in1, int in2)
{
    // handle method call org.freedesktop.login1.Manager.KillSession
    QMetaObject::invokeMethod(parent(), "KillSession", Q_ARG(QString, in0), Q_ARG(QString, in1), Q_ARG(int, in2));
}

void ManagerAdaptor::KillUser(uint in0, int in1)
{
    // handle method call org.freedesktop.login1.Manager.KillUser
    QMetaObject::invokeMethod(parent(), "KillUser", Q_ARG(uint, in0), Q_ARG(int, in1));
}

void ManagerAdaptor::LockSession(const QString &in0)
{
    // handle method call org.freedesktop.login1.Manager.LockSession
    QMetaObject::invokeMethod(parent(), "LockSession", Q_ARG(QString, in0));
}

void ManagerAdaptor::LockSessions()
{
    // handle method call org.freedesktop.login1.Manager.LockSessions
    QMetaObject::invokeMethod(parent(), "LockSessions");
}

void ManagerAdaptor::PowerOff(bool in0)
{
    // handle method call org.freedesktop.login1.Manager.PowerOff
    QMetaObject::invokeMethod(parent(), "PowerOff", Q_ARG(bool, in0));
}

void ManagerAdaptor::Reboot(bool in0)
{
    // handle method call org.freedesktop.login1.Manager.Reboot
    QMetaObject::invokeMethod(parent(), "Reboot", Q_ARG(bool, in0));
}

void ManagerAdaptor::ReleaseSession(const QString &in0)
{
    // handle method call org.freedesktop.login1.Manager.ReleaseSession
    QMetaObject::invokeMethod(parent(), "ReleaseSession", Q_ARG(QString, in0));
}

void ManagerAdaptor::ScheduleShutdown(const QString &in0, qulonglong in1)
{
    // handle method call org.freedesktop.login1.Manager.ScheduleShutdown
    QMetaObject::invokeMethod(parent(), "ScheduleShutdown", Q_ARG(QString, in0), Q_ARG(qulonglong, in1));
}

void ManagerAdaptor::SetRebootToFirmwareSetup(bool in0)
{
    // handle method call org.freedesktop.login1.Manager.SetRebootToFirmwareSetup
    QMetaObject::invokeMethod(parent(), "SetRebootToFirmwareSetup", Q_ARG(bool, in0));
}

void ManagerAdaptor::SetUserLinger(uint in0, bool in1, bool in2)
{
    // handle method call org.freedesktop.login1.Manager.SetUserLinger
    QMetaObject::invokeMethod(parent(), "SetUserLinger", Q_ARG(uint, in0), Q_ARG(bool, in1), Q_ARG(bool, in2));
}

void ManagerAdaptor::SetWallMessage(const QString &in0, bool in1)
{
    // handle method call org.freedesktop.login1.Manager.SetWallMessage
    QMetaObject::invokeMethod(parent(), "SetWallMessage", Q_ARG(QString, in0), Q_ARG(bool, in1));
}

void ManagerAdaptor::Suspend(bool in0)
{
    // handle method call org.freedesktop.login1.Manager.Suspend
    QMetaObject::invokeMethod(parent(), "Suspend", Q_ARG(bool, in0));
}

void ManagerAdaptor::TerminateSeat(const QString &in0)
{
    // handle method call org.freedesktop.login1.Manager.TerminateSeat
    QMetaObject::invokeMethod(parent(), "TerminateSeat", Q_ARG(QString, in0));
}

void ManagerAdaptor::TerminateSession(const QString &in0)
{
    // handle method call org.freedesktop.login1.Manager.TerminateSession
    QMetaObject::invokeMethod(parent(), "TerminateSession", Q_ARG(QString, in0));
}

void ManagerAdaptor::TerminateUser(uint in0)
{
    // handle method call org.freedesktop.login1.Manager.TerminateUser
    QMetaObject::invokeMethod(parent(), "TerminateUser", Q_ARG(uint, in0));
}

void ManagerAdaptor::UnlockSession(const QString &in0)
{
    // handle method call org.freedesktop.login1.Manager.UnlockSession
    QMetaObject::invokeMethod(parent(), "UnlockSession", Q_ARG(QString, in0));
}

void ManagerAdaptor::UnlockSessions()
{
    // handle method call org.freedesktop.login1.Manager.UnlockSessions
    QMetaObject::invokeMethod(parent(), "UnlockSessions");
}

