/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/libdbusmenuqt/com.canonical.dbusmenu.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/com.canonical.dbusmenuAdaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/com.canonical.dbusmenu.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef COM_CANONICAL_DBUSMENUADAPTOR_H
#define COM_CANONICAL_DBUSMENUADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/com.canonical.dbusmenu.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface com.canonical.dbusmenu
 */
class DbusmenuAdaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "com.canonical.dbusmenu")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"com.canonical.dbusmenu\">\n"
"    <property access=\"read\" type=\"u\" name=\"Version\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"Status\"/>\n"
"    <signal name=\"ItemsPropertiesUpdated\">\n"
"      <annotation value=\"DBusMenuItemList\" name=\"org.qtproject.QtDBus.QtTypeName.Out0\"/>\n"
"      <annotation value=\"DBusMenuItemKeysList\" name=\"org.qtproject.QtDBus.QtTypeName.Out1\"/>\n"
"      <arg direction=\"out\" type=\"a(ia{sv})\"/>\n"
"      <arg direction=\"out\" type=\"a(ias)\"/>\n"
"    </signal>\n"
"    <signal name=\"LayoutUpdated\">\n"
"      <arg direction=\"out\" type=\"u\" name=\"revision\"/>\n"
"      <arg direction=\"out\" type=\"i\" name=\"parentId\"/>\n"
"    </signal>\n"
"    <signal name=\"ItemActivationRequested\">\n"
"      <arg direction=\"out\" type=\"i\" name=\"id\"/>\n"
"      <arg direction=\"out\" type=\"u\" name=\"timeStamp\"/>\n"
"    </signal>\n"
"    <method name=\"Event\">\n"
"      <arg direction=\"in\" type=\"i\" name=\"id\"/>\n"
"      <arg direction=\"in\" type=\"s\" name=\"eventId\"/>\n"
"      <arg direction=\"in\" type=\"v\" name=\"data\"/>\n"
"      <arg direction=\"in\" type=\"u\" name=\"timestamp\"/>\n"
"      <annotation value=\"true\" name=\"org.freedesktop.DBus.Method.NoReply\"/>\n"
"    </method>\n"
"    <method name=\"GetProperty\">\n"
"      <arg direction=\"out\" type=\"v\"/>\n"
"      <arg direction=\"in\" type=\"i\" name=\"id\"/>\n"
"      <arg direction=\"in\" type=\"s\" name=\"property\"/>\n"
"    </method>\n"
"    <method name=\"GetLayout\">\n"
"      <arg direction=\"out\" type=\"u\"/>\n"
"      <arg direction=\"in\" type=\"i\" name=\"parentId\"/>\n"
"      <arg direction=\"in\" type=\"i\" name=\"recursionDepth\"/>\n"
"      <arg direction=\"in\" type=\"as\" name=\"propertyNames\"/>\n"
"      <arg direction=\"out\" type=\"(ia{sv}av)\" name=\"item\"/>\n"
"      <annotation value=\"DBusMenuLayoutItem\" name=\"org.qtproject.QtDBus.QtTypeName.Out1\"/>\n"
"    </method>\n"
"    <method name=\"GetGroupProperties\">\n"
"      <arg direction=\"out\" type=\"a(ia{sv})\"/>\n"
"      <annotation value=\"DBusMenuItemList\" name=\"org.qtproject.QtDBus.QtTypeName.Out0\"/>\n"
"      <arg direction=\"in\" type=\"ai\" name=\"ids\"/>\n"
"      <annotation value=\"QList&lt;int&gt;\" name=\"org.qtproject.QtDBus.QtTypeName.In0\"/>\n"
"      <arg direction=\"in\" type=\"as\" name=\"propertyNames\"/>\n"
"    </method>\n"
"    <method name=\"AboutToShow\">\n"
"      <arg direction=\"out\" type=\"b\"/>\n"
"      <arg direction=\"in\" type=\"i\" name=\"id\"/>\n"
"    </method>\n"
"  </interface>\n"
        "")
public:
    DbusmenuAdaptor(QObject *parent);
    virtual ~DbusmenuAdaptor();

public: // PROPERTIES
    Q_PROPERTY(QString Status READ status)
    QString status() const;

    Q_PROPERTY(uint Version READ version)
    uint version() const;

public Q_SLOTS: // METHODS
    bool AboutToShow(int id);
    Q_NOREPLY void Event(int id, const QString &eventId, const QDBusVariant &data, uint timestamp);
    DBusMenuItemList GetGroupProperties(const QList<int> &ids, const QStringList &propertyNames);
    uint GetLayout(int parentId, int recursionDepth, const QStringList &propertyNames, DBusMenuLayoutItem &item);
    QDBusVariant GetProperty(int id, const QString &property);
Q_SIGNALS: // SIGNALS
    void ItemActivationRequested(int id, uint timeStamp);
    void ItemsPropertiesUpdated(DBusMenuItemList in0, DBusMenuItemKeysList in1);
    void LayoutUpdated(uint revision, int parentId);
};

#endif
