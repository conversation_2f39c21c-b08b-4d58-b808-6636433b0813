/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/power/dbus/com.deepin.daemon.Power.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/com.deepin.daemon.PowerAdaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/com.deepin.daemon.Power.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef COM_DEEPIN_DAEMON_POWERADAPTOR_H
#define COM_DEEPIN_DAEMON_POWERADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/com.deepin.daemon.Power.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.deepin.dde.Power1
 */
class Power1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.dde.Power1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.dde.Power1\">\n"
"    <property access=\"read\" type=\"a{sd}\" name=\"BatteryPercentage\">\n"
"      <annotation value=\"BatteryPercentageMap\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"  </interface>\n"
        "")
public:
    Power1Adaptor(QObject *parent);
    virtual ~Power1Adaptor();

public: // PROPERTIES
    Q_PROPERTY(BatteryPercentageMap BatteryPercentage READ batteryPercentage)
    BatteryPercentageMap batteryPercentage() const;

public Q_SLOTS: // METHODS
Q_SIGNALS: // SIGNALS
};

#endif
