/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.deepin.dde.Audio1.Sink.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Audio1.SinkAdaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Audio1.Sink.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Audio1.SinkAdaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class SinkAdaptor
 */

SinkAdaptor::SinkAdaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

SinkAdaptor::~SinkAdaptor()
{
    // destructor
}

AudioPort SinkAdaptor::activePort() const
{
    // get the value of property ActivePort
    return qvariant_cast< AudioPort >(parent()->property("ActivePort"));
}

double SinkAdaptor::balance() const
{
    // get the value of property Balance
    return qvariant_cast< double >(parent()->property("Balance"));
}

double SinkAdaptor::baseVolume() const
{
    // get the value of property BaseVolume
    return qvariant_cast< double >(parent()->property("BaseVolume"));
}

uint SinkAdaptor::card() const
{
    // get the value of property Card
    return qvariant_cast< uint >(parent()->property("Card"));
}

QString SinkAdaptor::description() const
{
    // get the value of property Description
    return qvariant_cast< QString >(parent()->property("Description"));
}

double SinkAdaptor::fade() const
{
    // get the value of property Fade
    return qvariant_cast< double >(parent()->property("Fade"));
}

bool SinkAdaptor::mute() const
{
    // get the value of property Mute
    return qvariant_cast< bool >(parent()->property("Mute"));
}

QString SinkAdaptor::name() const
{
    // get the value of property Name
    return qvariant_cast< QString >(parent()->property("Name"));
}

AudioPortList SinkAdaptor::ports() const
{
    // get the value of property Ports
    return qvariant_cast< AudioPortList >(parent()->property("Ports"));
}

bool SinkAdaptor::supportBalance() const
{
    // get the value of property SupportBalance
    return qvariant_cast< bool >(parent()->property("SupportBalance"));
}

bool SinkAdaptor::supportFade() const
{
    // get the value of property SupportFade
    return qvariant_cast< bool >(parent()->property("SupportFade"));
}

double SinkAdaptor::volume() const
{
    // get the value of property Volume
    return qvariant_cast< double >(parent()->property("Volume"));
}

QDBusObjectPath SinkAdaptor::GetMeter()
{
    // handle method call org.deepin.dde.Audio1.Sink.GetMeter
    QDBusObjectPath out0;
    QMetaObject::invokeMethod(parent(), "GetMeter", Q_RETURN_ARG(QDBusObjectPath, out0));
    return out0;
}

void SinkAdaptor::SetBalance(double in0, bool in1)
{
    // handle method call org.deepin.dde.Audio1.Sink.SetBalance
    QMetaObject::invokeMethod(parent(), "SetBalance", Q_ARG(double, in0), Q_ARG(bool, in1));
}

void SinkAdaptor::SetFade(double in0)
{
    // handle method call org.deepin.dde.Audio1.Sink.SetFade
    QMetaObject::invokeMethod(parent(), "SetFade", Q_ARG(double, in0));
}

void SinkAdaptor::SetMute(bool in0)
{
    // handle method call org.deepin.dde.Audio1.Sink.SetMute
    QMetaObject::invokeMethod(parent(), "SetMute", Q_ARG(bool, in0));
}

void SinkAdaptor::SetPort(const QString &in0)
{
    // handle method call org.deepin.dde.Audio1.Sink.SetPort
    QMetaObject::invokeMethod(parent(), "SetPort", Q_ARG(QString, in0));
}

void SinkAdaptor::SetVolume(double in0, bool in1)
{
    // handle method call org.deepin.dde.Audio1.Sink.SetVolume
    QMetaObject::invokeMethod(parent(), "SetVolume", Q_ARG(double, in0), Q_ARG(bool, in1));
}

