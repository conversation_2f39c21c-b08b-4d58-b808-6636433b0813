/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/keyboard-layout/org.fcitx.Fcitx.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.fcitx.FcitxAdaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.fcitx.Fcitx.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.fcitx.FcitxAdaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class InputMethodAdaptor
 */

InputMethodAdaptor::InputMethodAdaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

InputMethodAdaptor::~InputMethodAdaptor()
{
    // destructor
}

QString InputMethodAdaptor::currentIM() const
{
    // get the value of property CurrentIM
    return qvariant_cast< QString >(parent()->property("CurrentIM"));
}

void InputMethodAdaptor::setCurrentIM(const QString &value)
{
    // set the value of property CurrentIM
    parent()->setProperty("CurrentIM", QVariant::fromValue(value));
}

FcitxQtInputMethodItemList InputMethodAdaptor::iMList() const
{
    // get the value of property IMList
    return qvariant_cast< FcitxQtInputMethodItemList >(parent()->property("IMList"));
}

void InputMethodAdaptor::setIMList(FcitxQtInputMethodItemList value)
{
    // set the value of property IMList
    parent()->setProperty("IMList", QVariant::fromValue(value));
}

void InputMethodAdaptor::ActivateIM()
{
    // handle method call org.fcitx.Fcitx.InputMethod.ActivateIM
    QMetaObject::invokeMethod(parent(), "ActivateIM");
}

void InputMethodAdaptor::Configure()
{
    // handle method call org.fcitx.Fcitx.InputMethod.Configure
    QMetaObject::invokeMethod(parent(), "Configure");
}

void InputMethodAdaptor::ConfigureAddon(const QString &addon)
{
    // handle method call org.fcitx.Fcitx.InputMethod.ConfigureAddon
    QMetaObject::invokeMethod(parent(), "ConfigureAddon", Q_ARG(QString, addon));
}

void InputMethodAdaptor::ConfigureIM(const QString &im)
{
    // handle method call org.fcitx.Fcitx.InputMethod.ConfigureIM
    QMetaObject::invokeMethod(parent(), "ConfigureIM", Q_ARG(QString, im));
}

int InputMethodAdaptor::CreateIC(uint &keyval1, uint &state1, uint &keyval2, uint &state2)
{
    // handle method call org.fcitx.Fcitx.InputMethod.CreateIC
    //return static_cast<YourObjectType *>(parent())->CreateIC(keyval1, state1, keyval2, state2);
}

int InputMethodAdaptor::CreateICv2(const QString &appname, bool &enable, uint &keyval1, uint &state1, uint &keyval2, uint &state2)
{
    // handle method call org.fcitx.Fcitx.InputMethod.CreateICv2
    //return static_cast<YourObjectType *>(parent())->CreateICv2(appname, enable, keyval1, state1, keyval2, state2);
}

int InputMethodAdaptor::CreateICv3(const QString &appname, int pid, bool &enable, uint &keyval1, uint &state1, uint &keyval2, uint &state2)
{
    // handle method call org.fcitx.Fcitx.InputMethod.CreateICv3
    //return static_cast<YourObjectType *>(parent())->CreateICv3(appname, pid, enable, keyval1, state1, keyval2, state2);
}

void InputMethodAdaptor::Exit()
{
    // handle method call org.fcitx.Fcitx.InputMethod.Exit
    QMetaObject::invokeMethod(parent(), "Exit");
}

QString InputMethodAdaptor::GetCurrentIM()
{
    // handle method call org.fcitx.Fcitx.InputMethod.GetCurrentIM
    QString im;
    QMetaObject::invokeMethod(parent(), "GetCurrentIM", Q_RETURN_ARG(QString, im));
    return im;
}

int InputMethodAdaptor::GetCurrentState()
{
    // handle method call org.fcitx.Fcitx.InputMethod.GetCurrentState
    int state;
    QMetaObject::invokeMethod(parent(), "GetCurrentState", Q_RETURN_ARG(int, state));
    return state;
}

QString InputMethodAdaptor::GetCurrentUI()
{
    // handle method call org.fcitx.Fcitx.InputMethod.GetCurrentUI
    QString addon;
    QMetaObject::invokeMethod(parent(), "GetCurrentUI", Q_RETURN_ARG(QString, addon));
    return addon;
}

QString InputMethodAdaptor::GetIMAddon(const QString &im)
{
    // handle method call org.fcitx.Fcitx.InputMethod.GetIMAddon
    QString addon;
    QMetaObject::invokeMethod(parent(), "GetIMAddon", Q_RETURN_ARG(QString, addon), Q_ARG(QString, im));
    return addon;
}

QString InputMethodAdaptor::GetIMByIndex(int index)
{
    // handle method call org.fcitx.Fcitx.InputMethod.GetIMByIndex
    QString im;
    QMetaObject::invokeMethod(parent(), "GetIMByIndex", Q_RETURN_ARG(QString, im), Q_ARG(int, index));
    return im;
}

void InputMethodAdaptor::InactivateIM()
{
    // handle method call org.fcitx.Fcitx.InputMethod.InactivateIM
    QMetaObject::invokeMethod(parent(), "InactivateIM");
}

void InputMethodAdaptor::ReloadAddonConfig(const QString &addon)
{
    // handle method call org.fcitx.Fcitx.InputMethod.ReloadAddonConfig
    QMetaObject::invokeMethod(parent(), "ReloadAddonConfig", Q_ARG(QString, addon));
}

void InputMethodAdaptor::ReloadConfig()
{
    // handle method call org.fcitx.Fcitx.InputMethod.ReloadConfig
    QMetaObject::invokeMethod(parent(), "ReloadConfig");
}

void InputMethodAdaptor::ResetIMList()
{
    // handle method call org.fcitx.Fcitx.InputMethod.ResetIMList
    QMetaObject::invokeMethod(parent(), "ResetIMList");
}

void InputMethodAdaptor::Restart()
{
    // handle method call org.fcitx.Fcitx.InputMethod.Restart
    QMetaObject::invokeMethod(parent(), "Restart");
}

void InputMethodAdaptor::SetCurrentIM(const QString &im)
{
    // handle method call org.fcitx.Fcitx.InputMethod.SetCurrentIM
    QMetaObject::invokeMethod(parent(), "SetCurrentIM", Q_ARG(QString, im));
}

void InputMethodAdaptor::SwitchIM()
{
    // handle method call org.fcitx.Fcitx.InputMethod.SwitchIM
    QMetaObject::invokeMethod(parent(), "SwitchIM");
}

void InputMethodAdaptor::ToggleIM()
{
    // handle method call org.fcitx.Fcitx.InputMethod.ToggleIM
    QMetaObject::invokeMethod(parent(), "ToggleIM");
}

