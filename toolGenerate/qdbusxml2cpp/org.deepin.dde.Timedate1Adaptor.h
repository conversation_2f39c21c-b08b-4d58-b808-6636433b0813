/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.deepin.dde.Timedate1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Timedate1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Timedate1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DEEPIN_DDE_TIMEDATE1ADAPTOR_H
#define ORG_DEEPIN_DDE_TIMEDATE1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Timedate1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.deepin.dde.Timedate1
 */
class Timedate1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.dde.Timedate1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.dde.Timedate1\">\n"
"    <signal name=\"TimeUpdate\"/>\n"
"    <method name=\"AddUserTimezone\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"DeleteUserTimezone\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"GetZoneInfo\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"out\" type=\"(ssi(xxi))\"/>\n"
"      <annotation value=\"ZoneInfo\" name=\"org.qtproject.QtDBus.QtTypeName.Out0\"/>\n"
"    </method>\n"
"    <method name=\"GetZoneList\">\n"
"      <arg direction=\"out\" type=\"as\"/>\n"
"    </method>\n"
"    <method name=\"GetSampleNTPServers\">\n"
"      <arg direction=\"out\" type=\"as\"/>\n"
"    </method>\n"
"    <method name=\"SetDate\">\n"
"      <arg direction=\"in\" type=\"i\"/>\n"
"      <arg direction=\"in\" type=\"i\"/>\n"
"      <arg direction=\"in\" type=\"i\"/>\n"
"      <arg direction=\"in\" type=\"i\"/>\n"
"      <arg direction=\"in\" type=\"i\"/>\n"
"      <arg direction=\"in\" type=\"i\"/>\n"
"      <arg direction=\"in\" type=\"i\"/>\n"
"    </method>\n"
"    <method name=\"SetLocalRTC\">\n"
"      <arg direction=\"in\" type=\"b\"/>\n"
"      <arg direction=\"in\" type=\"b\"/>\n"
"    </method>\n"
"    <method name=\"SetNTP\">\n"
"      <arg direction=\"in\" type=\"b\"/>\n"
"    </method>\n"
"    <method name=\"SetNTPServer\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"SetTime\">\n"
"      <arg direction=\"in\" type=\"x\"/>\n"
"      <arg direction=\"in\" type=\"b\"/>\n"
"    </method>\n"
"    <method name=\"SetTimezone\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <property access=\"read\" type=\"b\" name=\"CanNTP\"/>\n"
"    <property access=\"read\" type=\"b\" name=\"NTP\"/>\n"
"    <property access=\"read\" type=\"b\" name=\"LocalRTC\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"Timezone\"/>\n"
"    <property access=\"readwrite\" type=\"b\" name=\"Use24HourFormat\"/>\n"
"    <property access=\"readwrite\" type=\"i\" name=\"DSTOffset\"/>\n"
"    <property access=\"read\" type=\"as\" name=\"UserTimezones\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"NTPServer\"/>\n"
"    <property access=\"readwrite\" type=\"i\" name=\"WeekdayFormat\"/>\n"
"    <property access=\"readwrite\" type=\"i\" name=\"ShortDateFormat\"/>\n"
"    <property access=\"readwrite\" type=\"i\" name=\"LongDateFormat\"/>\n"
"    <property access=\"readwrite\" type=\"i\" name=\"ShortTimeFormat\"/>\n"
"    <property access=\"readwrite\" type=\"i\" name=\"LongTimeFormat\"/>\n"
"    <property access=\"readwrite\" type=\"i\" name=\"WeekBegins\"/>\n"
"  </interface>\n"
        "")
public:
    Timedate1Adaptor(QObject *parent);
    virtual ~Timedate1Adaptor();

public: // PROPERTIES
    Q_PROPERTY(bool CanNTP READ canNTP)
    bool canNTP() const;

    Q_PROPERTY(int DSTOffset READ dSTOffset WRITE setDSTOffset)
    int dSTOffset() const;
    void setDSTOffset(int value);

    Q_PROPERTY(bool LocalRTC READ localRTC)
    bool localRTC() const;

    Q_PROPERTY(int LongDateFormat READ longDateFormat WRITE setLongDateFormat)
    int longDateFormat() const;
    void setLongDateFormat(int value);

    Q_PROPERTY(int LongTimeFormat READ longTimeFormat WRITE setLongTimeFormat)
    int longTimeFormat() const;
    void setLongTimeFormat(int value);

    Q_PROPERTY(bool NTP READ nTP)
    bool nTP() const;

    Q_PROPERTY(QString NTPServer READ nTPServer)
    QString nTPServer() const;

    Q_PROPERTY(int ShortDateFormat READ shortDateFormat WRITE setShortDateFormat)
    int shortDateFormat() const;
    void setShortDateFormat(int value);

    Q_PROPERTY(int ShortTimeFormat READ shortTimeFormat WRITE setShortTimeFormat)
    int shortTimeFormat() const;
    void setShortTimeFormat(int value);

    Q_PROPERTY(QString Timezone READ timezone)
    QString timezone() const;

    Q_PROPERTY(bool Use24HourFormat READ use24HourFormat WRITE setUse24HourFormat)
    bool use24HourFormat() const;
    void setUse24HourFormat(bool value);

    Q_PROPERTY(QStringList UserTimezones READ userTimezones)
    QStringList userTimezones() const;

    Q_PROPERTY(int WeekBegins READ weekBegins WRITE setWeekBegins)
    int weekBegins() const;
    void setWeekBegins(int value);

    Q_PROPERTY(int WeekdayFormat READ weekdayFormat WRITE setWeekdayFormat)
    int weekdayFormat() const;
    void setWeekdayFormat(int value);

public Q_SLOTS: // METHODS
    void AddUserTimezone(const QString &in0);
    void DeleteUserTimezone(const QString &in0);
    QStringList GetSampleNTPServers();
    ZoneInfo GetZoneInfo(const QString &in0);
    QStringList GetZoneList();
    void SetDate(int in0, int in1, int in2, int in3, int in4, int in5, int in6);
    void SetLocalRTC(bool in0, bool in1);
    void SetNTP(bool in0);
    void SetNTPServer(const QString &in0);
    void SetTime(qlonglong in0, bool in1);
    void SetTimezone(const QString &in0);
Q_SIGNALS: // SIGNALS
    void TimeUpdate();
};

#endif
