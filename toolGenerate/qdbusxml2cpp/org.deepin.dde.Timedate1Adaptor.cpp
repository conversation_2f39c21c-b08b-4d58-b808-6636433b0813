/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.deepin.dde.Timedate1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Timedate1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Timedate1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Timedate1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class Timedate1Adaptor
 */

Timedate1Adaptor::Timedate1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

Timedate1Adaptor::~Timedate1Adaptor()
{
    // destructor
}

bool Timedate1Adaptor::canNTP() const
{
    // get the value of property CanNTP
    return qvariant_cast< bool >(parent()->property("CanNTP"));
}

int Timedate1Adaptor::dSTOffset() const
{
    // get the value of property DSTOffset
    return qvariant_cast< int >(parent()->property("DSTOffset"));
}

void Timedate1Adaptor::setDSTOffset(int value)
{
    // set the value of property DSTOffset
    parent()->setProperty("DSTOffset", QVariant::fromValue(value));
}

bool Timedate1Adaptor::localRTC() const
{
    // get the value of property LocalRTC
    return qvariant_cast< bool >(parent()->property("LocalRTC"));
}

int Timedate1Adaptor::longDateFormat() const
{
    // get the value of property LongDateFormat
    return qvariant_cast< int >(parent()->property("LongDateFormat"));
}

void Timedate1Adaptor::setLongDateFormat(int value)
{
    // set the value of property LongDateFormat
    parent()->setProperty("LongDateFormat", QVariant::fromValue(value));
}

int Timedate1Adaptor::longTimeFormat() const
{
    // get the value of property LongTimeFormat
    return qvariant_cast< int >(parent()->property("LongTimeFormat"));
}

void Timedate1Adaptor::setLongTimeFormat(int value)
{
    // set the value of property LongTimeFormat
    parent()->setProperty("LongTimeFormat", QVariant::fromValue(value));
}

bool Timedate1Adaptor::nTP() const
{
    // get the value of property NTP
    return qvariant_cast< bool >(parent()->property("NTP"));
}

QString Timedate1Adaptor::nTPServer() const
{
    // get the value of property NTPServer
    return qvariant_cast< QString >(parent()->property("NTPServer"));
}

int Timedate1Adaptor::shortDateFormat() const
{
    // get the value of property ShortDateFormat
    return qvariant_cast< int >(parent()->property("ShortDateFormat"));
}

void Timedate1Adaptor::setShortDateFormat(int value)
{
    // set the value of property ShortDateFormat
    parent()->setProperty("ShortDateFormat", QVariant::fromValue(value));
}

int Timedate1Adaptor::shortTimeFormat() const
{
    // get the value of property ShortTimeFormat
    return qvariant_cast< int >(parent()->property("ShortTimeFormat"));
}

void Timedate1Adaptor::setShortTimeFormat(int value)
{
    // set the value of property ShortTimeFormat
    parent()->setProperty("ShortTimeFormat", QVariant::fromValue(value));
}

QString Timedate1Adaptor::timezone() const
{
    // get the value of property Timezone
    return qvariant_cast< QString >(parent()->property("Timezone"));
}

bool Timedate1Adaptor::use24HourFormat() const
{
    // get the value of property Use24HourFormat
    return qvariant_cast< bool >(parent()->property("Use24HourFormat"));
}

void Timedate1Adaptor::setUse24HourFormat(bool value)
{
    // set the value of property Use24HourFormat
    parent()->setProperty("Use24HourFormat", QVariant::fromValue(value));
}

QStringList Timedate1Adaptor::userTimezones() const
{
    // get the value of property UserTimezones
    return qvariant_cast< QStringList >(parent()->property("UserTimezones"));
}

int Timedate1Adaptor::weekBegins() const
{
    // get the value of property WeekBegins
    return qvariant_cast< int >(parent()->property("WeekBegins"));
}

void Timedate1Adaptor::setWeekBegins(int value)
{
    // set the value of property WeekBegins
    parent()->setProperty("WeekBegins", QVariant::fromValue(value));
}

int Timedate1Adaptor::weekdayFormat() const
{
    // get the value of property WeekdayFormat
    return qvariant_cast< int >(parent()->property("WeekdayFormat"));
}

void Timedate1Adaptor::setWeekdayFormat(int value)
{
    // set the value of property WeekdayFormat
    parent()->setProperty("WeekdayFormat", QVariant::fromValue(value));
}

void Timedate1Adaptor::AddUserTimezone(const QString &in0)
{
    // handle method call org.deepin.dde.Timedate1.AddUserTimezone
    QMetaObject::invokeMethod(parent(), "AddUserTimezone", Q_ARG(QString, in0));
}

void Timedate1Adaptor::DeleteUserTimezone(const QString &in0)
{
    // handle method call org.deepin.dde.Timedate1.DeleteUserTimezone
    QMetaObject::invokeMethod(parent(), "DeleteUserTimezone", Q_ARG(QString, in0));
}

QStringList Timedate1Adaptor::GetSampleNTPServers()
{
    // handle method call org.deepin.dde.Timedate1.GetSampleNTPServers
    QStringList out0;
    QMetaObject::invokeMethod(parent(), "GetSampleNTPServers", Q_RETURN_ARG(QStringList, out0));
    return out0;
}

ZoneInfo Timedate1Adaptor::GetZoneInfo(const QString &in0)
{
    // handle method call org.deepin.dde.Timedate1.GetZoneInfo
    ZoneInfo out0;
    QMetaObject::invokeMethod(parent(), "GetZoneInfo", Q_RETURN_ARG(ZoneInfo, out0), Q_ARG(QString, in0));
    return out0;
}

QStringList Timedate1Adaptor::GetZoneList()
{
    // handle method call org.deepin.dde.Timedate1.GetZoneList
    QStringList out0;
    QMetaObject::invokeMethod(parent(), "GetZoneList", Q_RETURN_ARG(QStringList, out0));
    return out0;
}

void Timedate1Adaptor::SetDate(int in0, int in1, int in2, int in3, int in4, int in5, int in6)
{
    // handle method call org.deepin.dde.Timedate1.SetDate
    QMetaObject::invokeMethod(parent(), "SetDate", Q_ARG(int, in0), Q_ARG(int, in1), Q_ARG(int, in2), Q_ARG(int, in3), Q_ARG(int, in4), Q_ARG(int, in5), Q_ARG(int, in6));
}

void Timedate1Adaptor::SetLocalRTC(bool in0, bool in1)
{
    // handle method call org.deepin.dde.Timedate1.SetLocalRTC
    QMetaObject::invokeMethod(parent(), "SetLocalRTC", Q_ARG(bool, in0), Q_ARG(bool, in1));
}

void Timedate1Adaptor::SetNTP(bool in0)
{
    // handle method call org.deepin.dde.Timedate1.SetNTP
    QMetaObject::invokeMethod(parent(), "SetNTP", Q_ARG(bool, in0));
}

void Timedate1Adaptor::SetNTPServer(const QString &in0)
{
    // handle method call org.deepin.dde.Timedate1.SetNTPServer
    QMetaObject::invokeMethod(parent(), "SetNTPServer", Q_ARG(QString, in0));
}

void Timedate1Adaptor::SetTime(qlonglong in0, bool in1)
{
    // handle method call org.deepin.dde.Timedate1.SetTime
    QMetaObject::invokeMethod(parent(), "SetTime", Q_ARG(qlonglong, in0), Q_ARG(bool, in1));
}

void Timedate1Adaptor::SetTimezone(const QString &in0)
{
    // handle method call org.deepin.dde.Timedate1.SetTimezone
    QMetaObject::invokeMethod(parent(), "SetTimezone", Q_ARG(QString, in0));
}

