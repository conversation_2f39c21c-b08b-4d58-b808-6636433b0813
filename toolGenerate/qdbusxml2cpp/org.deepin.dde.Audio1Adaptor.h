/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.deepin.dde.Audio1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Audio1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Audio1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DEEPIN_DDE_AUDIO1ADAPTOR_H
#define ORG_DEEPIN_DDE_AUDIO1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Audio1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.deepin.dde.Audio1
 */
class Audio1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.dde.Audio1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.dde.Audio1\">\n"
"    <method name=\"Reset\"/>\n"
"    <method name=\"SetDefaultSink\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"SetDefaultSource\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"SetBluetoothAudioMode\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"SetPort\">\n"
"      <arg direction=\"in\" type=\"u\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"i\"/>\n"
"    </method>\n"
"    <method name=\"SetPortEnabled\">\n"
"      <arg direction=\"in\" type=\"u\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"b\"/>\n"
"    </method>\n"
"    <method name=\"IsPortEnabled\">\n"
"      <arg direction=\"in\" type=\"u\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"out\" type=\"b\"/>\n"
"    </method>\n"
"    <signal name=\"PortEnabledChanged\">\n"
"      <arg type=\"u\"/>\n"
"      <arg type=\"s\"/>\n"
"      <arg type=\"b\"/>\n"
"    </signal>\n"
"    <property access=\"read\" type=\"ao\" name=\"SinkInputs\"/>\n"
"    <property access=\"read\" type=\"ao\" name=\"Sinks\"/>\n"
"    <property access=\"read\" type=\"ao\" name=\"Sources\"/>\n"
"    <property access=\"read\" type=\"as\" name=\"BluetoothAudioModeOpts\">\n"
"      <annotation value=\"QStringList\" name=\"org.qtproject.QtDBus.QtTypeName\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"s\" name=\"BluetoothAudioMode\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"Cards\"/>\n"
"    <property access=\"read\" type=\"s\" name=\"CardsWithoutUnavailable\"/>\n"
"    <property access=\"read\" type=\"o\" name=\"DefaultSink\"/>\n"
"    <property access=\"read\" type=\"o\" name=\"DefaultSource\"/>\n"
"    <property access=\"read\" type=\"d\" name=\"MaxUIVolume\"/>\n"
"    <property access=\"readwrite\" type=\"b\" name=\"IncreaseVolume\"/>\n"
"    <property access=\"readwrite\" type=\"b\" name=\"ReduceNoise\"/>\n"
"  </interface>\n"
        "")
public:
    Audio1Adaptor(QObject *parent);
    virtual ~Audio1Adaptor();

public: // PROPERTIES
    Q_PROPERTY(QString BluetoothAudioMode READ bluetoothAudioMode)
    QString bluetoothAudioMode() const;

    Q_PROPERTY(QStringList BluetoothAudioModeOpts READ bluetoothAudioModeOpts)
    QStringList bluetoothAudioModeOpts() const;

    Q_PROPERTY(QString Cards READ cards)
    QString cards() const;

    Q_PROPERTY(QString CardsWithoutUnavailable READ cardsWithoutUnavailable)
    QString cardsWithoutUnavailable() const;

    Q_PROPERTY(QDBusObjectPath DefaultSink READ defaultSink)
    QDBusObjectPath defaultSink() const;

    Q_PROPERTY(QDBusObjectPath DefaultSource READ defaultSource)
    QDBusObjectPath defaultSource() const;

    Q_PROPERTY(bool IncreaseVolume READ increaseVolume WRITE setIncreaseVolume)
    bool increaseVolume() const;
    void setIncreaseVolume(bool value);

    Q_PROPERTY(double MaxUIVolume READ maxUIVolume)
    double maxUIVolume() const;

    Q_PROPERTY(bool ReduceNoise READ reduceNoise WRITE setReduceNoise)
    bool reduceNoise() const;
    void setReduceNoise(bool value);

    Q_PROPERTY(QList<QDBusObjectPath> SinkInputs READ sinkInputs)
    QList<QDBusObjectPath> sinkInputs() const;

    Q_PROPERTY(QList<QDBusObjectPath> Sinks READ sinks)
    QList<QDBusObjectPath> sinks() const;

    Q_PROPERTY(QList<QDBusObjectPath> Sources READ sources)
    QList<QDBusObjectPath> sources() const;

public Q_SLOTS: // METHODS
    bool IsPortEnabled(uint in0, const QString &in1);
    void Reset();
    void SetBluetoothAudioMode(const QString &in0);
    void SetDefaultSink(const QString &in0);
    void SetDefaultSource(const QString &in0);
    void SetPort(uint in0, const QString &in1, int in2);
    void SetPortEnabled(uint in0, const QString &in1, bool in2);
Q_SIGNALS: // SIGNALS
    void PortEnabledChanged(uint in0, const QString &in1, bool in2);
};

#endif
