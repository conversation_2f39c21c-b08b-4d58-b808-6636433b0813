/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/application-tray/api/dbus/org.deepin.dde.TrayManager1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.TrayManager1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.TrayManager1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.TrayManager1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class TrayManager1Adaptor
 */

TrayManager1Adaptor::TrayManager1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

TrayManager1Adaptor::~TrayManager1Adaptor()
{
    // destructor
}

TrayList TrayManager1Adaptor::trayIcons() const
{
    // get the value of property TrayIcons
    return qvariant_cast< TrayList >(parent()->property("TrayIcons"));
}

void TrayManager1Adaptor::EnableNotification(uint win, bool enabled)
{
    // handle method call org.deepin.dde.TrayManager1.EnableNotification
    QMetaObject::invokeMethod(parent(), "EnableNotification", Q_ARG(uint, win), Q_ARG(bool, enabled));
}

QString TrayManager1Adaptor::GetName(uint win)
{
    // handle method call org.deepin.dde.TrayManager1.GetName
    QString name;
    QMetaObject::invokeMethod(parent(), "GetName", Q_RETURN_ARG(QString, name), Q_ARG(uint, win));
    return name;
}

bool TrayManager1Adaptor::Manage()
{
    // handle method call org.deepin.dde.TrayManager1.Manage
    bool ok;
    QMetaObject::invokeMethod(parent(), "Manage", Q_RETURN_ARG(bool, ok));
    return ok;
}

