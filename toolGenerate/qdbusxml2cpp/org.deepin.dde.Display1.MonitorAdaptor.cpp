/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.deepin.dde.Display1.Monitor.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Display1.MonitorAdaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Display1.Monitor.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Display1.MonitorAdaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class MonitorAdaptor
 */

MonitorAdaptor::MonitorAdaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

MonitorAdaptor::~MonitorAdaptor()
{
    // destructor
}

QStringList MonitorAdaptor::availableFillModes() const
{
    // get the value of property AvailableFillModes
    return qvariant_cast< QStringList >(parent()->property("AvailableFillModes"));
}

Resolution MonitorAdaptor::bestMode() const
{
    // get the value of property BestMode
    return qvariant_cast< Resolution >(parent()->property("BestMode"));
}

bool MonitorAdaptor::connected() const
{
    // get the value of property Connected
    return qvariant_cast< bool >(parent()->property("Connected"));
}

QString MonitorAdaptor::currentFillMode() const
{
    // get the value of property CurrentFillMode
    return qvariant_cast< QString >(parent()->property("CurrentFillMode"));
}

void MonitorAdaptor::setCurrentFillMode(const QString &value)
{
    // set the value of property CurrentFillMode
    parent()->setProperty("CurrentFillMode", QVariant::fromValue(value));
}

Resolution MonitorAdaptor::currentMode() const
{
    // get the value of property CurrentMode
    return qvariant_cast< Resolution >(parent()->property("CurrentMode"));
}

uchar MonitorAdaptor::currentRotateMode() const
{
    // get the value of property CurrentRotateMode
    return qvariant_cast< uchar >(parent()->property("CurrentRotateMode"));
}

bool MonitorAdaptor::enabled() const
{
    // get the value of property Enabled
    return qvariant_cast< bool >(parent()->property("Enabled"));
}

ushort MonitorAdaptor::height() const
{
    // get the value of property Height
    return qvariant_cast< ushort >(parent()->property("Height"));
}

QString MonitorAdaptor::manufacturer() const
{
    // get the value of property Manufacturer
    return qvariant_cast< QString >(parent()->property("Manufacturer"));
}

uint MonitorAdaptor::mmHeight() const
{
    // get the value of property MmHeight
    return qvariant_cast< uint >(parent()->property("MmHeight"));
}

uint MonitorAdaptor::mmWidth() const
{
    // get the value of property MmWidth
    return qvariant_cast< uint >(parent()->property("MmWidth"));
}

QString MonitorAdaptor::model() const
{
    // get the value of property Model
    return qvariant_cast< QString >(parent()->property("Model"));
}

ResolutionList MonitorAdaptor::modes() const
{
    // get the value of property Modes
    return qvariant_cast< ResolutionList >(parent()->property("Modes"));
}

QString MonitorAdaptor::name() const
{
    // get the value of property Name
    return qvariant_cast< QString >(parent()->property("Name"));
}

ushort MonitorAdaptor::reflect() const
{
    // get the value of property Reflect
    return qvariant_cast< ushort >(parent()->property("Reflect"));
}

ReflectList MonitorAdaptor::reflects() const
{
    // get the value of property Reflects
    return qvariant_cast< ReflectList >(parent()->property("Reflects"));
}

double MonitorAdaptor::refreshRate() const
{
    // get the value of property RefreshRate
    return qvariant_cast< double >(parent()->property("RefreshRate"));
}

ushort MonitorAdaptor::rotation() const
{
    // get the value of property Rotation
    return qvariant_cast< ushort >(parent()->property("Rotation"));
}

RotationList MonitorAdaptor::rotations() const
{
    // get the value of property Rotations
    return qvariant_cast< RotationList >(parent()->property("Rotations"));
}

ushort MonitorAdaptor::width() const
{
    // get the value of property Width
    return qvariant_cast< ushort >(parent()->property("Width"));
}

short MonitorAdaptor::x() const
{
    // get the value of property X
    return qvariant_cast< short >(parent()->property("X"));
}

short MonitorAdaptor::y() const
{
    // get the value of property Y
    return qvariant_cast< short >(parent()->property("Y"));
}

void MonitorAdaptor::Enable(bool in0)
{
    // handle method call org.deepin.dde.Display1.Monitor.Enable
    QMetaObject::invokeMethod(parent(), "Enable", Q_ARG(bool, in0));
}

void MonitorAdaptor::SetMode(uint in0)
{
    // handle method call org.deepin.dde.Display1.Monitor.SetMode
    QMetaObject::invokeMethod(parent(), "SetMode", Q_ARG(uint, in0));
}

void MonitorAdaptor::SetModeBySize(ushort in0, ushort in1)
{
    // handle method call org.deepin.dde.Display1.Monitor.SetModeBySize
    QMetaObject::invokeMethod(parent(), "SetModeBySize", Q_ARG(ushort, in0), Q_ARG(ushort, in1));
}

void MonitorAdaptor::SetPosition(short in0, short in1)
{
    // handle method call org.deepin.dde.Display1.Monitor.SetPosition
    QMetaObject::invokeMethod(parent(), "SetPosition", Q_ARG(short, in0), Q_ARG(short, in1));
}

void MonitorAdaptor::SetReflect(ushort in0)
{
    // handle method call org.deepin.dde.Display1.Monitor.SetReflect
    QMetaObject::invokeMethod(parent(), "SetReflect", Q_ARG(ushort, in0));
}

void MonitorAdaptor::SetRotation(ushort in0)
{
    // handle method call org.deepin.dde.Display1.Monitor.SetRotation
    QMetaObject::invokeMethod(parent(), "SetRotation", Q_ARG(ushort, in0));
}

