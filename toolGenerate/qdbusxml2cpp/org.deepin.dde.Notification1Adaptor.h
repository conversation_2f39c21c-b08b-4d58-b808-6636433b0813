/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.deepin.dde.Notification1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Notification1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Notification1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DEEPIN_DDE_NOTIFICATION1ADAPTOR_H
#define ORG_DEEPIN_DDE_NOTIFICATION1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Notification1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.deepin.dde.Notification1
 */
class Notification1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.dde.Notification1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.dde.Notification1\">\n"
"    <method name=\"CloseNotification\">\n"
"      <arg direction=\"in\" type=\"u\"/>\n"
"    </method>\n"
"    <method name=\"GetCapbilities\">\n"
"      <arg direction=\"out\" type=\"as\"/>\n"
"    </method>\n"
"    <method name=\"GetServerInformation\">\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"Notify\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"u\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"as\"/>\n"
"      <arg direction=\"in\" type=\"a{sv}\"/>\n"
"      <annotation value=\"QVariantMap\" name=\"org.qtproject.QtDBus.QtTypeName.In6\"/>\n"
"      <arg direction=\"in\" type=\"i\"/>\n"
"      <arg direction=\"out\" type=\"u\"/>\n"
"    </method>\n"
"    <method name=\"GetAllRecords\">\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"GetRecordById\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"GetRecordsFromId\">\n"
"      <arg direction=\"in\" type=\"i\"/>\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"RemoveRecord\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"ClearRecords\"/>\n"
"    <method name=\"getAppSetting\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"out\" type=\"s\"/>\n"
"    </method>\n"
"    <method name=\"Toggle\"/>\n"
"    <method name=\"Show\"/>\n"
"    <method name=\"Hide\"/>\n"
"    <method name=\"recordCount\">\n"
"      <arg direction=\"out\" type=\"u\"/>\n"
"    </method>\n"
"    <method name=\"GetAppList\">\n"
"      <arg direction=\"out\" type=\"as\"/>\n"
"    </method>\n"
"    <method name=\"GetAppInfo\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"u\"/>\n"
"      <arg direction=\"out\" type=\"v\"/>\n"
"    </method>\n"
"    <method name=\"GetSystemInfo\">\n"
"      <arg direction=\"in\" type=\"u\"/>\n"
"      <arg direction=\"out\" type=\"v\"/>\n"
"    </method>\n"
"    <method name=\"SetAppInfo\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"      <arg direction=\"in\" type=\"u\"/>\n"
"      <arg direction=\"in\" type=\"v\"/>\n"
"    </method>\n"
"    <method name=\"SetSystemInfo\">\n"
"      <arg direction=\"in\" type=\"u\"/>\n"
"      <arg direction=\"in\" type=\"v\"/>\n"
"    </method>\n"
"    <method name=\"setAppSetting\">\n"
"      <arg direction=\"in\" type=\"s\"/>\n"
"    </method>\n"
"    <signal name=\"NotificationClosed\">\n"
"      <arg type=\"u\"/>\n"
"      <arg type=\"u\"/>\n"
"    </signal>\n"
"    <signal name=\"ActionInvoked\">\n"
"      <arg type=\"u\"/>\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"RecordAdded\">\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"AppInfoChanged\">\n"
"      <arg type=\"s\"/>\n"
"      <arg type=\"u\"/>\n"
"      <arg type=\"v\"/>\n"
"    </signal>\n"
"    <signal name=\"SystemInfoChanged\">\n"
"      <arg type=\"u\"/>\n"
"      <arg type=\"v\"/>\n"
"    </signal>\n"
"    <signal name=\"AppAddedSignal\">\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"AppRemovedSignal\">\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"appRemoved\">\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"appAdded\">\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"appSettingChanged\">\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <signal name=\"systemSettingChanged\">\n"
"      <arg type=\"s\"/>\n"
"    </signal>\n"
"    <property access=\"readwrite\" type=\"s\" name=\"allSetting\"/>\n"
"    <property access=\"readwrite\" type=\"s\" name=\"systemSetting\"/>\n"
"  </interface>\n"
        "")
public:
    Notification1Adaptor(QObject *parent);
    virtual ~Notification1Adaptor();

public: // PROPERTIES
    Q_PROPERTY(QString allSetting READ allSetting WRITE setAllSetting)
    QString allSetting() const;
    void setAllSetting(const QString &value);

    Q_PROPERTY(QString systemSetting READ systemSetting WRITE setSystemSetting)
    QString systemSetting() const;
    void setSystemSetting(const QString &value);

public Q_SLOTS: // METHODS
    void ClearRecords();
    void CloseNotification(uint in0);
    QString GetAllRecords();
    QDBusVariant GetAppInfo(const QString &in0, uint in1);
    QStringList GetAppList();
    QStringList GetCapbilities();
    QString GetRecordById(const QString &in0);
    QString GetRecordsFromId(int in0, const QString &in1);
    QString GetServerInformation(QString &out1, QString &out2, QString &out3);
    QDBusVariant GetSystemInfo(uint in0);
    void Hide();
    uint Notify(const QString &in0, uint in1, const QString &in2, const QString &in3, const QString &in4, const QStringList &in5, const QVariantMap &in6, int in7);
    void RemoveRecord(const QString &in0);
    void SetAppInfo(const QString &in0, uint in1, const QDBusVariant &in2);
    void SetSystemInfo(uint in0, const QDBusVariant &in1);
    void Show();
    void Toggle();
    QString getAppSetting(const QString &in0);
    uint recordCount();
    void setAppSetting(const QString &in0);
Q_SIGNALS: // SIGNALS
    void ActionInvoked(uint in0, const QString &in1);
    void AppAddedSignal(const QString &in0);
    void AppInfoChanged(const QString &in0, uint in1, const QDBusVariant &in2);
    void AppRemovedSignal(const QString &in0);
    void NotificationClosed(uint in0, uint in1);
    void RecordAdded(const QString &in0);
    void SystemInfoChanged(uint in0, const QDBusVariant &in1);
    void appAdded(const QString &in0);
    void appRemoved(const QString &in0);
    void appSettingChanged(const QString &in0);
    void systemSettingChanged(const QString &in0);
};

#endif
