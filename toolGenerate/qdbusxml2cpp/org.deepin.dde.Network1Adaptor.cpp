/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-tray-loader/plugins/dde-dock/dbus/xml/org.deepin.dde.Network1.xml -a ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Network1Adaptor -i ./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Network1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-tray-loader/toolGenerate/qdbusxml2cpp/org.deepin.dde.Network1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class Network1Adaptor
 */

Network1Adaptor::Network1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

Network1Adaptor::~Network1Adaptor()
{
    // destructor
}

QString Network1Adaptor::activeConnections() const
{
    // get the value of property ActiveConnections
    return qvariant_cast< QString >(parent()->property("ActiveConnections"));
}

QString Network1Adaptor::connections() const
{
    // get the value of property Connections
    return qvariant_cast< QString >(parent()->property("Connections"));
}

uint Network1Adaptor::connectivity() const
{
    // get the value of property Connectivity
    return qvariant_cast< uint >(parent()->property("Connectivity"));
}

QString Network1Adaptor::devices() const
{
    // get the value of property Devices
    return qvariant_cast< QString >(parent()->property("Devices"));
}

bool Network1Adaptor::networkingEnabled() const
{
    // get the value of property NetworkingEnabled
    return qvariant_cast< bool >(parent()->property("NetworkingEnabled"));
}

void Network1Adaptor::setNetworkingEnabled(bool value)
{
    // set the value of property NetworkingEnabled
    parent()->setProperty("NetworkingEnabled", QVariant::fromValue(value));
}

uint Network1Adaptor::state() const
{
    // get the value of property State
    return qvariant_cast< uint >(parent()->property("State"));
}

bool Network1Adaptor::vpnEnabled() const
{
    // get the value of property VpnEnabled
    return qvariant_cast< bool >(parent()->property("VpnEnabled"));
}

void Network1Adaptor::setVpnEnabled(bool value)
{
    // set the value of property VpnEnabled
    parent()->setProperty("VpnEnabled", QVariant::fromValue(value));
}

QString Network1Adaptor::wirelessAccessPoints() const
{
    // get the value of property WirelessAccessPoints
    return qvariant_cast< QString >(parent()->property("WirelessAccessPoints"));
}

QDBusObjectPath Network1Adaptor::ActivateAccessPoint(const QString &in0, const QDBusObjectPath &in1, const QDBusObjectPath &in2)
{
    // handle method call org.deepin.dde.Network1.ActivateAccessPoint
    QDBusObjectPath out0;
    QMetaObject::invokeMethod(parent(), "ActivateAccessPoint", Q_RETURN_ARG(QDBusObjectPath, out0), Q_ARG(QString, in0), Q_ARG(QDBusObjectPath, in1), Q_ARG(QDBusObjectPath, in2));
    return out0;
}

QDBusObjectPath Network1Adaptor::ActivateConnection(const QString &in0, const QDBusObjectPath &in1)
{
    // handle method call org.deepin.dde.Network1.ActivateConnection
    QDBusObjectPath out0;
    QMetaObject::invokeMethod(parent(), "ActivateConnection", Q_RETURN_ARG(QDBusObjectPath, out0), Q_ARG(QString, in0), Q_ARG(QDBusObjectPath, in1));
    return out0;
}

void Network1Adaptor::CancelSecret(const QString &in0, const QString &in1)
{
    // handle method call org.deepin.dde.Network1.CancelSecret
    QMetaObject::invokeMethod(parent(), "CancelSecret", Q_ARG(QString, in0), Q_ARG(QString, in1));
}

QDBusObjectPath Network1Adaptor::CreateConnection(const QString &in0, const QDBusObjectPath &in1)
{
    // handle method call org.deepin.dde.Network1.CreateConnection
    QDBusObjectPath out0;
    QMetaObject::invokeMethod(parent(), "CreateConnection", Q_RETURN_ARG(QDBusObjectPath, out0), Q_ARG(QString, in0), Q_ARG(QDBusObjectPath, in1));
    return out0;
}

QDBusObjectPath Network1Adaptor::CreateConnectionForAccessPoint(const QDBusObjectPath &in0, const QDBusObjectPath &in1)
{
    // handle method call org.deepin.dde.Network1.CreateConnectionForAccessPoint
    QDBusObjectPath out0;
    QMetaObject::invokeMethod(parent(), "CreateConnectionForAccessPoint", Q_RETURN_ARG(QDBusObjectPath, out0), Q_ARG(QDBusObjectPath, in0), Q_ARG(QDBusObjectPath, in1));
    return out0;
}

void Network1Adaptor::DeactivateConnection(const QString &in0)
{
    // handle method call org.deepin.dde.Network1.DeactivateConnection
    QMetaObject::invokeMethod(parent(), "DeactivateConnection", Q_ARG(QString, in0));
}

void Network1Adaptor::DeleteConnection(const QString &in0)
{
    // handle method call org.deepin.dde.Network1.DeleteConnection
    QMetaObject::invokeMethod(parent(), "DeleteConnection", Q_ARG(QString, in0));
}

void Network1Adaptor::DisableWirelessHotspotMode(const QDBusObjectPath &in0)
{
    // handle method call org.deepin.dde.Network1.DisableWirelessHotspotMode
    QMetaObject::invokeMethod(parent(), "DisableWirelessHotspotMode", Q_ARG(QDBusObjectPath, in0));
}

void Network1Adaptor::DisconnectDevice(const QDBusObjectPath &in0)
{
    // handle method call org.deepin.dde.Network1.DisconnectDevice
    QMetaObject::invokeMethod(parent(), "DisconnectDevice", Q_ARG(QDBusObjectPath, in0));
}

QDBusObjectPath Network1Adaptor::EditConnection(const QString &in0, const QDBusObjectPath &in1)
{
    // handle method call org.deepin.dde.Network1.EditConnection
    QDBusObjectPath out0;
    QMetaObject::invokeMethod(parent(), "EditConnection", Q_RETURN_ARG(QDBusObjectPath, out0), Q_ARG(QString, in0), Q_ARG(QDBusObjectPath, in1));
    return out0;
}

void Network1Adaptor::EnableDevice(const QDBusObjectPath &in0, bool in1)
{
    // handle method call org.deepin.dde.Network1.EnableDevice
    QMetaObject::invokeMethod(parent(), "EnableDevice", Q_ARG(QDBusObjectPath, in0), Q_ARG(bool, in1));
}

void Network1Adaptor::EnableWirelessHotspotMode(const QDBusObjectPath &in0)
{
    // handle method call org.deepin.dde.Network1.EnableWirelessHotspotMode
    QMetaObject::invokeMethod(parent(), "EnableWirelessHotspotMode", Q_ARG(QDBusObjectPath, in0));
}

void Network1Adaptor::FeedSecret(const QString &in0, const QString &in1, const QString &in2, bool in3)
{
    // handle method call org.deepin.dde.Network1.FeedSecret
    QMetaObject::invokeMethod(parent(), "FeedSecret", Q_ARG(QString, in0), Q_ARG(QString, in1), Q_ARG(QString, in2), Q_ARG(bool, in3));
}

QString Network1Adaptor::GetAccessPoints(const QDBusObjectPath &in0)
{
    // handle method call org.deepin.dde.Network1.GetAccessPoints
    QString out0;
    QMetaObject::invokeMethod(parent(), "GetAccessPoints", Q_RETURN_ARG(QString, out0), Q_ARG(QDBusObjectPath, in0));
    return out0;
}

QString Network1Adaptor::GetActiveConnectionInfo()
{
    // handle method call org.deepin.dde.Network1.GetActiveConnectionInfo
    QString out0;
    QMetaObject::invokeMethod(parent(), "GetActiveConnectionInfo", Q_RETURN_ARG(QString, out0));
    return out0;
}

QString Network1Adaptor::GetAutoProxy()
{
    // handle method call org.deepin.dde.Network1.GetAutoProxy
    QString out0;
    QMetaObject::invokeMethod(parent(), "GetAutoProxy", Q_RETURN_ARG(QString, out0));
    return out0;
}

QString Network1Adaptor::GetProxy(const QString &in0, QString &out1)
{
    // handle method call org.deepin.dde.Network1.GetProxy
    //return static_cast<YourObjectType *>(parent())->GetProxy(in0, out1);
}

QString Network1Adaptor::GetProxyIgnoreHosts()
{
    // handle method call org.deepin.dde.Network1.GetProxyIgnoreHosts
    QString out0;
    QMetaObject::invokeMethod(parent(), "GetProxyIgnoreHosts", Q_RETURN_ARG(QString, out0));
    return out0;
}

QString Network1Adaptor::GetProxyMethod()
{
    // handle method call org.deepin.dde.Network1.GetProxyMethod
    QString out0;
    QMetaObject::invokeMethod(parent(), "GetProxyMethod", Q_RETURN_ARG(QString, out0));
    return out0;
}

QStringList Network1Adaptor::GetSupportedConnectionTypes()
{
    // handle method call org.deepin.dde.Network1.GetSupportedConnectionTypes
    QStringList out0;
    QMetaObject::invokeMethod(parent(), "GetSupportedConnectionTypes", Q_RETURN_ARG(QStringList, out0));
    return out0;
}

QString Network1Adaptor::GetWiredConnectionUuid(const QDBusObjectPath &in0)
{
    // handle method call org.deepin.dde.Network1.GetWiredConnectionUuid
    QString out0;
    QMetaObject::invokeMethod(parent(), "GetWiredConnectionUuid", Q_RETURN_ARG(QString, out0), Q_ARG(QDBusObjectPath, in0));
    return out0;
}

bool Network1Adaptor::IsDeviceEnabled(const QDBusObjectPath &in0)
{
    // handle method call org.deepin.dde.Network1.IsDeviceEnabled
    bool out0;
    QMetaObject::invokeMethod(parent(), "IsDeviceEnabled", Q_RETURN_ARG(bool, out0), Q_ARG(QDBusObjectPath, in0));
    return out0;
}

bool Network1Adaptor::IsPasswordValid(const QString &in0, const QString &in1)
{
    // handle method call org.deepin.dde.Network1.IsPasswordValid
    bool out0;
    QMetaObject::invokeMethod(parent(), "IsPasswordValid", Q_RETURN_ARG(bool, out0), Q_ARG(QString, in0), Q_ARG(QString, in1));
    return out0;
}

bool Network1Adaptor::IsWirelessHotspotModeEnabled(const QDBusObjectPath &in0)
{
    // handle method call org.deepin.dde.Network1.IsWirelessHotspotModeEnabled
    bool out0;
    QMetaObject::invokeMethod(parent(), "IsWirelessHotspotModeEnabled", Q_RETURN_ARG(bool, out0), Q_ARG(QDBusObjectPath, in0));
    return out0;
}

QList<QDBusObjectPath> Network1Adaptor::ListDeviceConnections(const QDBusObjectPath &in0)
{
    // handle method call org.deepin.dde.Network1.ListDeviceConnections
    QList<QDBusObjectPath> out0;
    QMetaObject::invokeMethod(parent(), "ListDeviceConnections", Q_RETURN_ARG(QList<QDBusObjectPath>, out0), Q_ARG(QDBusObjectPath, in0));
    return out0;
}

void Network1Adaptor::RegisterSecretReceiver()
{
    // handle method call org.deepin.dde.Network1.RegisterSecretReceiver
    QMetaObject::invokeMethod(parent(), "RegisterSecretReceiver");
}

void Network1Adaptor::RequestIPConflictCheck(const QString &in0, const QString &in1)
{
    // handle method call org.deepin.dde.Network1.RequestIPConflictCheck
    QMetaObject::invokeMethod(parent(), "RequestIPConflictCheck", Q_ARG(QString, in0), Q_ARG(QString, in1));
}

void Network1Adaptor::RequestWirelessScan()
{
    // handle method call org.deepin.dde.Network1.RequestWirelessScan
    QMetaObject::invokeMethod(parent(), "RequestWirelessScan");
}

void Network1Adaptor::SetAutoProxy(const QString &in0)
{
    // handle method call org.deepin.dde.Network1.SetAutoProxy
    QMetaObject::invokeMethod(parent(), "SetAutoProxy", Q_ARG(QString, in0));
}

void Network1Adaptor::SetDeviceManaged(const QString &in0, bool in1)
{
    // handle method call org.deepin.dde.Network1.SetDeviceManaged
    QMetaObject::invokeMethod(parent(), "SetDeviceManaged", Q_ARG(QString, in0), Q_ARG(bool, in1));
}

void Network1Adaptor::SetProxy(const QString &in0, const QString &in1, const QString &in2)
{
    // handle method call org.deepin.dde.Network1.SetProxy
    QMetaObject::invokeMethod(parent(), "SetProxy", Q_ARG(QString, in0), Q_ARG(QString, in1), Q_ARG(QString, in2));
}

void Network1Adaptor::SetProxyIgnoreHosts(const QString &in0)
{
    // handle method call org.deepin.dde.Network1.SetProxyIgnoreHosts
    QMetaObject::invokeMethod(parent(), "SetProxyIgnoreHosts", Q_ARG(QString, in0));
}

void Network1Adaptor::SetProxyMethod(const QString &in0)
{
    // handle method call org.deepin.dde.Network1.SetProxyMethod
    QMetaObject::invokeMethod(parent(), "SetProxyMethod", Q_ARG(QString, in0));
}

