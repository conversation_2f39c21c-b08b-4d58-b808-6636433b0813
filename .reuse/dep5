Format: https://www.debian.org/doc/packaging-manuals/copyright-format/1.0/
Upstream-Name: dde-launchpad
Upstream-Contact: UnionTech Software Technology Co., Ltd.  <>
Source: https://github.com/linuxdeepin/dde-launchpad

# packaging manifests
Files: debian/* rpm/* archlinux/* .packit.yaml
Copyright: None
License: CC0-1.0

# cmake
Files: misc/*.in cmake/*.cmake cmake/DdeDock/*.in cmake/DdeTrayLoader/*.in *.in
Copyright: None
License: CC0-1.0

# configuration files for CI scripts/tools
Files: .github/* .obs/* .tx/transifex.yaml
Copyright: None
License: CC0-1.0

# README and documentations
Files: README.md README.*.md docs/*.md *guide.md */libdbusmenuqt/README */libdbusmenuqt/test/README
Copyright: UnionTech Software Technology Co., Ltd.
License: CC-BY-4.0

# images
Files: */resources/*.png */icons/*.svg */resources/*.svg */texts/*.svg */texts/*.svg.background */dark/*.svg */light/*.svg */actions/*.svg */images/*.png */icon/*.svg */res/*.svg */icons/*.dci */resources/*.dci */icon/*.dci */res/*.dci
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# resource sheet
Files: *.qrc */qmldir
Copyright: None
License: CC0-1.0

# translations
Files: */translations/*.ts
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# dbus api xml files
Files: *.deepin.dde.*.xml */org.desktopspec.*.xml */dbus/*.xml *.Fcitx.xml */libdbusmenuqt/*.xml
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

Files: src/loader/configs/*.xml src/protocol/*.xml
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

Files: */session/*.conf */system/*.conf
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# package json
Files: *.json
Copyright: None
License: CC0-1.0

# test bash
Files: *.sh
Copyright: None
License: CC0-1.0

# services
Files: misc/*.service
Copyright: None
License: CC0-1.0

Files: toolGenerate/**/*
Copyright: None
License: CC0-1.0
