usr/libexec/trayplugin-loader
usr/lib/*/libdde-trayplugin-interface.so.*
usr/share/dde-dock/translations
usr/share/trayplugin-loader/translations
usr/share/dsg/configs/*/*.json
usr/lib/*/qt6/plugins/wayland-shell-integration/libplugin-shell.so
usr/lib/dde-dock/plugins/libapplication-tray.so
usr/lib/dde-dock/plugins/libbrightness.so
usr/lib/dde-dock/plugins/libdatetime.so
usr/lib/dde-dock/plugins/libdnd-mode.so
usr/lib/dde-dock/plugins/libeye-comfort-mode.so
usr/lib/dde-dock/plugins/libmedia.so
usr/lib/dde-dock/plugins/libnotification.so
usr/lib/dde-dock/plugins/libonboard.so
usr/lib/dde-dock/plugins/libshutdown.so
usr/lib/dde-dock/plugins/system-trays/
usr/lib/dde-dock/plugins/system-trays/libairplane-mode.so
usr/lib/dde-dock/plugins/system-trays/libbluetooth.so
usr/lib/dde-dock/plugins/system-trays/libkeyboard-layout.so
usr/lib/dde-dock/plugins/system-trays/libpower.so
usr/lib/dde-dock/plugins/system-trays/libsound.so
usr/share/dde-dock/icons/dcc-setting/dcc-airplane-mode.dci
usr/share/dde-dock/icons/dcc-setting/dcc-battery.dci
usr/share/dde-dock/icons/dcc-setting/dcc-bluetooth.dci
usr/share/dde-dock/icons/dcc-setting/dcc-datetime.dci
usr/share/dde-dock/icons/dcc-setting/dcc-dde-brightness.dci
usr/share/dde-dock/icons/dcc-setting/dcc-dnd-mode.dci
usr/share/dde-dock/icons/dcc-setting/dcc-eye-comfort-mode.dci
usr/share/dde-dock/icons/dcc-setting/dcc-notification.dci
usr/share/dde-dock/icons/dcc-setting/dcc-onboard.dci
usr/share/dde-dock/icons/dcc-setting/dcc-shutdown.dci
