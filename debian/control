Source: dde-tray-loader
Section: unknown
Priority: optional
Maintainer: Deepin Packages Builder <<EMAIL>>
Build-Depends: debhelper-compat ( =12),
 pkg-config,
 cmake,
 extra-cmake-modules,
 qt6-wayland-dev,
 qt6-wayland-dev-tools,
 qt6-wayland-private-dev,
 qt6-base-dev-tools,
 qt6-base-private-dev,
 qt6-svg-dev,
 qt6-tools-dev,
 libwayland-dev,
 libxcb-image0-dev,
 libxcb-composite0-dev,
 libxcb-ewmh-dev,
 libxtst-dev,
 libxcb-xtest0-dev,
 libxcb-res0-dev,
 libxcb-util-dev,
 libxcb-icccm4-dev,
 libxcb-damage0-dev,
 libdtk6widget-dev,
 libdtk6core-dev,
 libdtk6core-bin,
 libdtk6gui-dev,
 libgtest-dev,
 libgmock-dev,
 libxcursor-dev,
 dde-api-dev (>>5.6.3)
Standards-Version: 3.9.8
Homepage: http://www.deepin.org/

Package: dde-tray-loader
Architecture: any
Depends: ${shlibs:Depends}, ${misc:Depends},
 deepin-desktop-schemas (>=5.9.14),
 libdtk6widget,
 libdtk6core,
 libdtk6gui,
 dde-qt6xcb-plugin,
 dde-daemon (>=6.1.26),
 lastore-daemon (>=5.2.9),
Conflicts:
 dde-dock,
 dde-dock-onboard-plugin
Replaces:
 dde-dock,
 dde-dock-onboard-plugin
Description: deepin desktop-environment - the plugins of tray for dde-dock
 Tray plugins module

Package: dde-tray-loader-dev
Architecture: any
Depends: ${shlibs:Depends}, ${misc:Depends}
Conflicts:
 dde-dock-dev
Replaces:
 dde-dock-dev
Provides:
 dde-dock-dev (=6.1.0)
Description: deepin desktop-environment - tray plugins module development files
 Tray plugins module development files of deepin desktop-environment

Package: dde-wirelesscasting-plugin
Architecture: any
Depends: ${shlibs:Depends}, ${misc:Depends}
Description: deepin desktop-environment - wireless casting plugin for dde-dock
 wireless casting plugin module
