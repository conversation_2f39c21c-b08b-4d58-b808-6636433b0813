dde-tray-loader (2.0.5) unstable; urgency=medium

  * fix: adjust height calculation order in BluetoothApplet
  * feat: implement custom PageButton for calendar navigation

 -- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>  Wed, 16 Jul 2025 10:47:44 +0800

dde-tray-loader (2.0.4) unstable; urgency=medium

  * i18n: Translate dde-dock.ts in fr (#335)
  * feat: add Traditional Chinese translations for tray plugin

 -- <PERSON><PERSON><PERSON>gYu <<EMAIL>>  Thu, 10 Jul 2025 20:15:53 +0800

dde-tray-loader (2.0.3) unstable; urgency=medium

  * fix: add hardening build flags to debian/rules
  * i18n: Updates for project Deepin Desktop Environment (#331)
  * i18n: Updates for project Deepin Desktop Environment (#328)

 -- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>  Thu, 03 Jul 2025 19:59:38 +0800

dde-tray-loader (2.0.2) unstable; urgency=medium

  * chore: remove unused Spanish translation files

 -- <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>  Mon, 23 Jun 2025 17:28:46 +0800

dde-tray-loader (2.0.1) unstable; urgency=medium

  * refactor: replace DIconButton with DToolButton for calendar
    navigation

 -- <PERSON><PERSON><PERSON><PERSON><PERSON> <yeshan<PERSON>@uniontech.com>  Thu, 19 Jun 2025 17:31:50 +0800

dde-tray-loader (2.0.0) unstable; urgency=medium

  * i18n: Updates for project Deepin Desktop Environment (#324)

 -- YeShanShan <<EMAIL>>  Thu, 19 Jun 2025 10:18:17 +0800

dde-tray-loader (1.99.28) unstable; urgency=medium

  * fix: timeDate plugin crash
  * fix: dateTime plugin only responds to short format changes from dde-control-center
  * [skip CI] Translate dde-dock.ts in es (#319)

 -- zhangkun <<EMAIL>>  Thu, 12 Jun 2025 19:48:15 +0800

dde-tray-loader (1.99.27) unstable; urgency=medium

  * refactor: move dock context menu implementation to loader module
  * fix: update appid of lastore daemon
  * i18n: Updates for project Deepin Desktop Environment (#314)
  * fix: sound plugin display incorrect volume tips
  * fix: when hide plugin,setting button sholud be hided.

 -- zhangkun <<EMAIL>>  Thu, 29 May 2025 19:10:45 +0800

dde-tray-loader (1.99.26) unstable; urgency=medium

  * refactor: reorder power mode options in performance controller

 -- YeShanShan <<EMAIL>>  Tue, 13 May 2025 20:56:53 +0800

dde-tray-loader (1.99.25) unstable; urgency=medium

  * Revert "fix: close applet popup after bluetooth adaptor was removed"
  * fix: close applet popup after bluetooth adaptor was removed
  * i18n: Updates for project Deepin Desktop Environment (#304)
  * fix: airplane plugin does not show in tray area
  * fix: playing sound when dragging sound slider released
  * Revert "fix: playing sound when sound slider released (#286)"
  * fix: app crashed by recursive called for theme palette
  * fix: datetime plugin display incorrect week fromat
  * chore: remove no used code
  * fix: datetime plugin tips display Incorrect time format
  * fix: datetime plugin show incorrect time format

 -- YeShanShan <<EMAIL>>  Thu, 08 May 2025 17:44:32 +0800

dde-tray-loader (1.99.24) unstable; urgency=medium

  * fix: improve tray plugin color handling and initialization
  * fix: close applet popup after bluetooth adaptor was removed
  * fix: airplane mode not clickable in bluetooth applet
  * i18n: Updates for project Deepin Desktop Environment (#291)
  * fix: update onboard display to show completely when zoomed in.
  * fix: loss unread status for notification

 -- YeShanShan <<EMAIL>>  Tue, 29 Apr 2025 14:50:33 +0800
 
dde-tray-loader (1.99.23) UNRELEASED; urgency=medium

  * fix: notification status does not changed by shortcut
  * i18n: Updates for project Deepin Desktop Environment (#287)
  * fix: playing sound when sound slider released (#286)
  * i18n: Updates for project Deepin Desktop Environment (#284)
  * chore: adjust cmake base version to 1.99.0

 -- YeShanShan <<EMAIL>>  Thu, 17 Apr 2025 19:19:55 +0800

dde-tray-loader (1.99.22) UNRELEASED; urgency=medium

  * i18n: Updates for project Deepin Desktop Environment (#279)
  * fix: remove dependency of deepin-network-display to migrate qt6
  * feat: Remove network module
  * fix: DndMode state doesn't follow change with dde-control-center
  * fix: remove qt5 dependencies
  * fix: prevent submenu item hover event propagation from main menu
  * fix: submenu can't display
  * i18n: Updates for project Deepin Desktop Environment (#275)

 -- YeShanShan <<EMAIL>>  Thu, 10 Apr 2025 19:27:54 +0800

dde-tray-loader (1.99.21) UNRELEASED; urgency=medium

  * chore: remove startdde dependency

 -- fuleyi <<EMAIL>>  Wed, 02 Apr 2025 11:33:41 +0800

dde-tray-loader (1.99.20) UNRELEASED; urgency=medium
  
  * [skip CI] Translate dde-dock.ts in tr
  * fix: missing null pointer judgment
  * chore: remove wireless casting module

 -- YeShanShan <<EMAIL>>  Fri, 14 Mar 2025 13:18:20 +0800

dde-tray-loader (1.99.19) UNRELEASED; urgency=medium

  * fix: power popup icon size is too small

 -- Deepin Packages Builder <<EMAIL>>  Fri, 14 Mar 2025 13:18:20 +0800

dde-tray-loader (1.99.18) UNRELEASED; urgency=medium

  * fix: dateTime plugin's hover background height is too high
  * fix: dateTime plugin menu function invalid

 -- Deepin Packages Builder <<EMAIL>>  Tue, 11 Mar 2025 15:11:20 +0800

dde-tray-loader (1.99.17) UNRELEASED; urgency=medium

  * chore: bump version to 1.99.17

 -- Deepin Packages Builder <<EMAIL>>  Thu, 06 Mar 2025 19:55:27 +0800

dde-tray-loader (1.99.16) UNRELEASED; urgency=medium

  * chore: bump version to 1.99.16

 -- Deepin Packages Builder <<EMAIL>>  Fri, 28 Feb 2025 10:25:27 +0800

dde-tray-loader (1.99.15) UNRELEASED; urgency=medium

  * fix: Have an audio output device, but the volume display is muted(Bug: 298251)

 -- Deepin Packages Builder <<EMAIL>>  Fri, 14 Feb 2025 10:42:27 +0800

dde-tray-loader (1.99.14) UNRELEASED; urgency=medium

  * fix: can't reset oldEnv.
  * fix: wrong environment for children process
  * fix: small black block when there is no compositor

 -- Deepin Packages Builder <<EMAIL>>  Thu, 23 Jan 2025 18:09:19 +0800

dde-tray-loader (1.99.13) UNRELEASED; urgency=medium

  * fix: small black block when there is no compositor

 -- tsic404 <<EMAIL>>  Fri, 17 Jan 2025 14:48:55 +0800

dde-tray-loader (1.99.12) UNRELEASED; urgency=medium

  * Temporary disable some plugins for Treeland

 -- Wang Zichong <<EMAIL>>  Thu, 16 Jan 2025 12:07:00 +0800

dde-tray-loader (1.99.11) UNRELEASED; urgency=medium

  * fix: Datetime plugin display problem (#230)(Bug: 276275)

 -- Deepin Packages Builder <<EMAIL>>  Fri, 03 Jan 2025 13:35:05 +0800

dde-tray-loader (1.99.10) UNRELEASED; urgency=medium

  * UNRELEASED

 -- Deepin Packages Builder <<EMAIL>>  Tue, 24 Dec 2024 20:23:04 +0800

dde-tray-loader (1.99.9) UNRELEASED; urgency=medium

  * fix: cooperation-networkDisPlay service is not available(Bug: 294901)
  * fix: Shutdown plugin miss menu function(Bug: 294875)

 -- Deepin Packages Builder <<EMAIL>>  Thu, 19 Dec 2024 10:40:05 +0800

dde-tray-loader (1.99.8) UNRELEASED; urgency=medium

  * UNRELEASED

 -- Deepin Packages Builder <<EMAIL>>  Sat, 14 Dec 2024 14:51:05 +0800

dde-tray-loader (1.99.7) UNRELEASED; urgency=medium

  * fix: can not jump to dcc bluetooth page(Bug: 289763)
  * fix: Support loading multiple plugins(Bug: 283901)

 -- Deepin Packages Builder <<EMAIL>>  Fri, 06 Dec 2024 15:26:46 +0800

dde-tray-loader (1.99.6) UNRELEASED; urgency=medium

  * fix: Bluetooth list mouse left click does not work (#204)(Bug: 284405)
  * fix: can not open calendar(Bug: 266927)

 -- Deepin Packages Builder <<EMAIL>>  Fri, 29 Nov 2024 11:23:16 +0800

dde-tray-loader (1.99.5) unstable; urgency=medium

  * feat: support for requeset shutdown

 -- xionglinlin <<EMAIL>>  Wed, 20 Nov 2024 16:12:11 +0800

dde-tray-loader (1.99.4) unstable; urgency=medium

  * chore: adapt to qt6

 -- zhaoyingzhen <<EMAIL>>  Wed, 20 Nov 2024 10:49:55 +0800

dde-tray-loader (1.99.3) UNRELEASED; urgency=medium

  * fix: normalize the naming of the deb package

 -- xionglinlin <<EMAIL>>  Fri, 08 Nov 2024 15:47:41 +0800

dde-tray-loader (1.99.2) UNRELEASED; urgency=medium

  * bump version to 1.99.2

 -- YeShanShan <<EMAIL>>  Thu, 31 Oct 2024 10:34:35 +0800

dde-tray-loader (1.99.1) UNRELEASED; urgency=medium

  * fix: Music icon is too small in quick panel on High scale ratio screen(Bug: 272035)
  * style:  Adjust pointer spacing in moveEvent declaration
  * fix: remove useless function (#143)
    Thanks to Hillwood Yang
  * feat: Power module uses DConfig instead of QGSettings
  * chore: fix code style problem
  * fix: build failed
  * feat: support switching new and old notification(task: 35599)
  * fix: notification number doesn't change(task: 365219)
  * feat: switching icon when notification status changed(task: 365893)
  * fix: the indicator of notification state doesn't work(task: 365893)

 -- YeShanShan <<EMAIL>>  Wed, 23 Oct 2024 10:09:52 +0800

dde-tray-loader (1.0.0) unstable; urgency=medium

  * Fix incomplete display of Bluetooth plug-in prompts
    (linuxdeepin/developer-center#10479)
  * Fix crash on TreeLand
  * Update translation from Transifex

 -- Wang Zichong <<EMAIL>>  Mon, 02 Sep 2024 16:22:00 +0800

dde-tray-loader (0.0.12) unstable; urgency=medium

  * fix: Updated ordering of performance modes for power supplies (#151)(Issue: 10112)
  * fix: ap label font color not incorrect(Issue: 10460)
  * fix: Synchronized network translation(Issue: 10066)
  * fix: sni protocol item has no icon (#154)(Issue: 10144)
  * fix: plugin item menu does not have the option 'remove from dock'(Issue: 10359)
  * fix: tooltip margin and tooltip disappears abnormally(Issue: 10478)

 -- Deepin Packages Builder <<EMAIL>>  Thu, 29 Aug 2024 10:14:21 +0800

dde-tray-loader (0.0.11) unstable; urgency=medium

  * fix: adding tray icon blocks app launch(Issue: 10439)
  * feat: add destroy protocol(Issue: 10121)

 -- Deepin Packages Builder <<EMAIL>>  Wed, 21 Aug 2024 17:02:04 +0800

dde-tray-loader (0.0.10) unstable; urgency=medium

  * fix: the text color is incorrect(Issue: 10106)

 -- Deepin Packages Builder <<EMAIL>>  Tue, 13 Aug 2024 20:45:53 +0800

dde-tray-loader (0.0.9) unstable; urgency=medium

  * fix: Fixed the right-click display settings cannot jump to the control center (#136)(Issue: 10336)
  * fix: Remove the Airplane Mode setting option from the Control Center (#137)(Issue: 10335)
  * fix: tooltip content cannot update(Issue: 10361)

 -- Deepin Packages Builder <<EMAIL>>  Tue, 13 Aug 2024 16:00:51 +0800

dde-tray-loader (0.0.8) unstable; urgency=medium

  * bump version to 0.0.8

 -- Deepin Packages Builder <<EMAIL>>  Mon, 12 Aug 2024 17:30:11 +0800

dde-tray-loader (0.0.7) unstable; urgency=medium

  * fix:  clicking quickItem plugin with the left mouse button donot work(Influence: left mouse clicking quickItem)
  * fix: The DBus service name has been modified(Issue: 10018)
  * fix: When modifying the VPN, the wireless network icon does not display(Issue: 10195)
  * fix: Modify network non automatic reconnection issue(Issue: 10184)
  * fix: Translation issue not installed when modifying user acquisition error(Issue: 10018)
  * fix: quickpanel show empty if click tray's item(Issue: 10229)
  * chore: removing print too many logs(Influence: none)
  * fix: app crashed cannot generate coredump file(Influence: app crashed has coredump file)

 -- Deepin Packages Builder <<EMAIL>>  Thu, 08 Aug 2024 15:08:41 +0800

dde-tray-loader (0.0.6) unstable; urgency=medium

  * fix: adjust tooltip display(Issue: 10043)
  * fix: Cursor instantly move to left side(Issue: 9987)
  * fix: date text color does not change with theme(Issue: 10104)
  * fix: bluetooth devices list width(Issue: 9981)
  * fix: Menu display incomplete(Issue: 9721 9729)
  * fix: Popup position cannot be refreshed with dock(Issue: 10096)
  * fix: Error in modifying IP address display(Issue: 10073)
  * fix: update dde-dock plugins translate(Issue: 10123 10012)
  * fix: Modify the issue of dock plugin crashing when double clicking(Issue: 10118 10067)
  * fix: at high PixelRatio, the datetime display is too large
  * fix: subMenu can't show
  * fix: The margin issue of Tooltip(Issue: 10005 9997)

 -- Zhang kun <<EMAIL>>  Tue, 06 Aug 2024 15:41:06 +0800

dde-tray-loader (0.0.5) unstable; urgency=medium

  * fix: onboard plugin is not installed(Issue: 9820)
  * fix: ToolTip is positioned to the left(Issue: 9953)
  * fix: update taskbar plug-in translation (#88)(Issue: 9893)
  * description: Power switching mode adds performance mode switching(Issue: 9893)
  * fix: Fixed dnd-mode settings cannot jump to the Control Center(Issue: 9988)
  * feat: request isActive state for quickpanel(Issue: 10020)
  * fix: right button also emits clicked for quickpanel(Issue: 9960)
  * chore: refactor the dateTime plugin display code (#93)(Influence: datetime plugin display)
  * fix: dateTime panel popup date and 12hour format display does not match the settings (#96)(Influence: datetime plugin display)
  * fix: removing jump to dcc's airplane mode(Issue: 9990)(Influence: bluetooth can not jump to dcc's airplane mode)
  * fix: Fixed  the power performance mode initialization dnot get init value (#99)(Issue: 10019)

 -- zyz <<EMAIL>>  Wed, 31 Jul 2024 14:07:12 +0800

dde-tray-loader (0.0.4) unstable; urgency=medium

  * fix: network view size cannot respond to content changes in a timely manner(Issue: 9881)(Influence: network view size)
  * fix: Update translation file path(Issue: 9956)
  * fix: datetime plugin set 24-hour format do not work(Issue: 9717, 9857)(Influence: datetime plugin display)

 -- zyz <<EMAIL>>  Thu, 25 Jul 2024 16:23:30 +0800

dde-tray-loader (0.0.3) unstable; urgency=medium

  * feat: plugin item can set content margin (#65)(Influence: plugin item display)
  * chore: update the eye comfort mode icon(Issue: 9875)
  * fix: EyeComfortMode plugin can switch theme type (#69)(Issue: 9691)(Influence: can switch themme type)
  * fix: Fixed the issue that setting custom time format in taskbar does not take effect(Issue: 9724)
  * fix: Added trayplugin-loader translation(Issue: 9754, 9685)
  * fix: Modifying proxy icon does not display issue(Issue: 9888)
  * fix: applicatin-tray coredump while open wechat and wework(issue: 9918)
  * fix: Hide Airplane mode settings in popup applet(Issue: 9884)

 -- zyz <<EMAIL>>  Wed, 24 Jul 2024 14:31:44 +0800

dde-tray-loader (0.0.2) unstable; urgency=medium

  * fix: datetime plugin in DCC dosn't have icon
  * fix: Responding to proxy change signals (#58)

 -- Yingzhen zhao <<EMAIL>>  Fri, 19 Jul 2024 17:32:20 +0800

dde-tray-loader (0.0.1) unstable; urgency=medium

  * chore: add dde-dock.pc
  * FIX 1070 errors
  * fix: compile errors
  * fix: solve the problem of failed compilation
  * chore: add plugins and loader
  * chore: add the README

 -- Wang Fei <<EMAIL>>  Wed, 26 Jun 2024 11:33:24 +0800

dde-tray-loader (0.0.0) unstable; urgency=medium

  * 0.0.0

 -- Wang Fei <<EMAIL>>  Tue, 25 Jun 2024 16:53:15 +0800
