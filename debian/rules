#!/usr/bin/make -f
DPKG_EXPORT_BUILDFLAGS = 1
include /usr/share/dpkg/default.mk
export QT_SELECT = qt5
export DEB_BUILD_MAINT_OPTIONS = hardening=+all
export DEB_CFLAGS_MAINT_APPEND = -Wall
export DEB_CXXFLAGS_MAINT_APPEND = -Wall
export DEB_LDFLAGS_MAINT_APPEND = -Wl,--as-needed -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -Wl,-E

DEB_HOST_MULTIARCH ?= $(shell dpkg-architecture -qDEB_HOST_MULTIARCH)

VERSION = $(DEB_VERSION_UPSTREAM)
PACK_VER = $(shell echo $(VERSION) | awk -F'[+_~-]' '{print $$1}')
# Fix: invalid digit "8" in octal constant. e.g.  u008 ==> 008 ==> 8
BUILD_VER = $(shell echo $(VERSION) | awk -F'[+_~-]' '{print $$2}' | sed 's/[^0-9]//g' | awk '{print int($$1)}')

%:
	dh $@

override_dh_auto_configure:
	dh_auto_configure -- -DDTL_VERSION=${PACK_VER}
