<?xml version="1.0" encoding="UTF-8"?>
<protocol name="plugin_manager_v1">
  <copyright>
      Copyright © 2024 Uniontech

      Permission is hereby granted, free of charge, to any person obtaining a
      copy of this software and associated documentation files (the "Software"),
      to deal in the Software without restriction, including without limitation
      the rights to use, copy, modify, merge, publish, distribute, sublicense,
      and/or sell copies of the Software, and to permit persons to whom the
      Software is furnished to do so, subject to the following conditions:

      The above copyright notice and this permission notice (including the next
      paragraph) shall be included in all copies or substantial portions of the
      Software.

      THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
      IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
      FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
      THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
      LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
      FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
      DEALINGS IN THE SOFTWARE.
  </copyright>
  <interface name="plugin_manager_v1" version="1">
    <description summary="plugin manager">
      This interface allows a client to get server(dock) some info and create plugins to compositor.
    </description>
    <request name="destroy" type="destructor">
    </request>
  
    <enum name="error">
      <entry name="role"            value="0" summary="given wl_surface has another role"/>
      <entry name="plugin_id"       value="1" summary="given wl_surface has another plugin_id"/>
    </enum>

    <event name="position_changed">
        <arg name="dock_position" type="uint"/>
    </event>

    <event name="color_theme_changed">
        <arg name="dock_color_theme" type="uint"/>
    </event>

    <event name="active_color_changed">
        <description summary="active color">
          sync active color from server.
        </description>
        <arg name="active_color" type="string"/>
        <arg name="dark_active_color" type="string"/>
    </event>

    <event name="font_changed">
        <description summary="font information">
          sync font information from server.
        </description>
        <arg name="font_name" type="string"/>
        <arg name="font_point_size" type="int"/>
    </event>

    <event name="theme_changed">
        <description summary="theme information">
          sync theme information from server.
        </description>
        <arg name="theme_name" type="string"/>
        <arg name="icon_theme_name" type="string"/>
    </event>

    <event name="event_message">
      <description summary="a message sent from server (dock)">
        This event will emitted when server(dock) send message to plugin.
        Such as dock position/themeType/overflowStatus and other event defined in server(dock) interface
      </description>
      <arg name="msg"  type="string"/>
    </event>

    <request name="request_message">
      <description summary="a message sent from client">
        This event will emitted when client send message to server.
        Such as plugin status interface defined.
      </description>
      <arg name="plugin_id"  type="string"/>
      <arg name="item_key"  type="string"/>
      <arg name="msg"  type="string"/>
    </request>

    <request name="create_popup_at">
      <description summary="created popup window">
        This will created a popup window at (x, y) pos relative to ouput.
      </description>
      <arg name="plugin_id"     type="string"/>
      <arg name="item_key"      type="string"/>
      <arg name="type"          type="int"/>
      <arg name="x"             type="int"/>
      <arg name="y"             type="int"/>

      <arg name="surface" type="object" interface="wl_surface"/>
      <arg name="id"      type="new_id" interface="plugin_popup"/>
    </request>

    <request name="create_plugin">
      <arg name="plugin_id"     type="string"/>
      <arg name="item_key"      type="string"/>
      <arg name="display_name"  type="string"/>
      <arg name="plugin_flags"  type="int"/>
      <arg name="type"          type="int"/>
      <arg name="size_policy"   type="int"/>

      <arg name="surface" type="object" interface="wl_surface"/>
      <arg name="id"      type="new_id" interface="plugin"/>
    </request>
  </interface>
  <interface name="plugin_popup" version="1">
    <request name="destroy" type="destructor">
    </request>
    <event name="close"/>
    <event name="geometry">
      <arg name="x" type="int"/>
      <arg name="y" type="int"/>
      <arg name="width" type="int"/>
      <arg name="height" type="int"/>
    </event>
    <request name="set_position" >
        <arg name="x" type="int"/>
        <arg name="y" type="int"/>
    </request>
    <request name="source_size" >
        <arg name="width" type="int"/>
        <arg name="height" type="int"/>
    </request>
  </interface>

  <interface name="plugin" version="1">
    <request name="destroy" type="destructor">
    </request>
    <event name="close"/>
    <event name="geometry">
      <arg name="x" type="int"/>
      <arg name="y" type="int"/>
      <arg name="width" type="int"/>
      <arg name="height" type="int"/>
    </event>
    <event name="margin">
      <arg name="spacing" type="int"/>
    </event>
    <event name="raw_global_pos">
      <arg name="x" type="int"/>
      <arg name="y" type="int"/>
    </event>
    <request name="mouse_event" >
        <arg name="type" type="int"/>
    </request>
    <request name="dcc_icon" >
        <arg name="type" type="string"/>
    </request>
    <request name="request_shutdown">
        <arg name="type" type="string"/>
    </request>
    <request name="source_size" >
        <arg name="width" type="int"/>
        <arg name="height" type="int"/>
    </request>
    <request name="close_quick_panel" />
  </interface>
</protocol>
