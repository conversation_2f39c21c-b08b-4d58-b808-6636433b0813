<?xml version='1.0' encoding='utf-8'?>
<schemalist>
    <enum id="com.deepin.dde.control-center.PasswordType">
        <value nick="NormalPassWord" value="0" />
        <value nick="IncludeBlankSymbol" value="1" />
    </enum>
    <enum id="com.deepin.dde.control-center.StatusMode">
        <value nick="Enabled" value="0" />
        <value nick="Disabled" value="1" />
        <value nick="Hidden" value="2" />
    </enum>
    <schema id="com.deepin.dde.control-center" path="/com/deepin/dde/control-center/">
        <key enum="com.deepin.dde.control-center.StatusMode" name="mainwindow-search-edit">
            <default>'Enabled'</default>
            <summary>Set search edit state</summary>
        <description>控制中心搜索框控件的启用、显示</description></key>
        <key name="brightness-minimum" type="d">
            <default>0.1</default>
            <range max="1.0" min="0.0" />
            <summary>The minimum brightness rate</summary>
        <description>亮度调节的最小值</description></key>
        <key name="auto-exit" type="b">
            <default>false</default>
            <summary>auto-exit</summary>
        <description>废弃</description></key>
        <key name="window-width" type="i">
            <default>820</default>
            <range max="9999" min="820" />
            <summary>The window width last time</summary>
        <description>不对外</description></key>
        <key name="window-height" type="i">
            <default>634</default>
            <range max="9999" min="634" />
            <summary>The window height last time</summary>
        <description>不对外</description></key>
        <key name="wait-sound-receipt" type="i">
            <default>1000</default>
            <range max="9999" min="1" />
            <summary>Waiting for sound receipt Wait 1 second by default</summary>
        <description>声音-输入、输出-输入、输出设备选择后等待时间（1000-9999ms）</description></key>
        <key name="hide-module" type="as">
            <default>[]</default>
            <summary>The module to display</summary>
        <description>控制中心一级菜单隐藏数组，菜单名称&lt;accounts,bluetooth,commoninfo,datetime,defapp,commoninfo,display
,keyboard,mouse,notification,personalization,power,privacy,keyboard,cloudsync,systeminfo,touchcreen,wacom,update,passkey&gt;</description></key>
        <key name="show-createuser" type="b">
            <default>true</default>
            <summary>The create user module show or not</summary>
        <description>账户-账户列表下方添加账户按钮的显示</description></key>
        <key name="show-multiscreen" type="b">
            <default>true</default>
            <summary>The multiscreen setting show or not</summary>
        <description>显示-多屏设置相关控件的显示</description></key>
        <key name="show-suspend" type="b">
            <default>true</default>
            <summary>The suspend options show or not</summary>
        <description>电源管理-使用电源、使用电池-按电源按钮时控件选项是否包含待机选项、电脑进入待机模式控件的显示</description></key>
        <key name="effect-load" type="b">
            <default>false</default>
            <summary>Magiclamp is loaded or not</summary>
        <description>个性化-"最小化效果"选项是否为魔灯</description></key>
        <key name="effective-day-visible" type="b">
            <default>true</default>
	    <summary>display the permission to set password validity days</summary>
	<description>“密码有效天数”控件的显示</description></key>
        <key name="auto-login-visable" type="b">
            <default>true</default>
            <summary>Account module automatic login visable control</summary>
        <description>"自动登录"控件显示</description></key>
        <key name="nopasswd-login-visable" type="b">
            <default>true</default>
            <summary>Account module no password login visable control</summary>
        <description>账户-无密码登录的显示</description></key>
        <key enum="com.deepin.dde.control-center.PasswordType" name="account-password-type">
            <default>'NormalPassWord'</default>
            <summary />
            <summary>Account password verification control</summary>
            <description>废弃</description>
        </key>
        <key name="brightness-enable" type="b">
            <default>true</default>
            <summary>Display module show brightness settings or not</summary>
        <description>"亮度"显示</description></key>
        <key name="plugin-setting" type="s">
            <default>'{\"mainwindow\":{\"voice\":{\"enable\":true,\"index\":\"keyboard\",\"maxmenu\":3}}}'</default>
            <summary>plugin settings about position</summary>
        <description>废弃</description></key>
        <key name="show-hibernate" type="b">
            <default>true</default>
            <summary>The hibernate options show or not</summary>
        <description>电源管理-使用电源、使用电池-按电源按钮时控件选项是否包含休眠选项</description></key>
        <key name="show-shutdown" type="b">
            <default>true</default>
            <summary>The shutdown options show or not</summary>
        <description>电源管理-使用电源、使用电池-按电源按钮时控件选项是否包含关机选项</description></key>
        <key name="custom-ntpserver" type="s">
            <default>''</default>
            <summary>Used to record custom ntp server</summary>
        <description>设置时间服务器自定义状态默认地址</description></key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="display-multiple-displays">
            <default>'Enabled'</default>
            <summary>change display multiple displays status</summary>
            <description>显示-"多屏设置"里面所有选项的启用、显示 【需要连接多个显示器】</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="display-light-lighting">
            <default>'Enabled'</default>
            <summary>change display lighting button status</summary>
            <description>显示-"亮度"里面所有选项的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="display-color-temperature">
            <default>'Enabled'</default>
            <summary>change display color Temperature status</summary>
            <description>显示-"色温"里面所有选项的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="display-scaling">
            <default>'Enabled'</default>
            <summary>change display scaling status</summary>
            <description>显示-"屏幕缩放"里面所有选项的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="display-resolution">
            <default>'Enabled'</default>
            <summary>change display resolution status</summary>
            <description>显示-"分辨率"控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="display-refresh-rate">
            <default>'Enabled'</default>
            <summary>change display refresh rate status</summary>
            <description>显示-"刷新率"控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="display-rotate">
            <default>'Enabled'</default>
            <summary>change display rotate status</summary>
            <description>显示-"方向"控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="sound-output-slider">
            <default>'Enabled'</default>
            <summary>change sound output slider status</summary>
            <description>声音-输出-输出音量控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="sound-volume-boost">
            <default>'Enabled'</default>
            <summary>change sound volume boost status</summary>
            <description>声音-输出-音量增强控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="sound-balance-slider">
            <default>'Enabled'</default>
            <summary>change sound balance slider status</summary>
            <description>声音-输出-左/右平衡控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="sound-input-slider">
            <default>'Enabled'</default>
            <summary>change sound input slider status</summary>
            <description>声音-输入-输入音量控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="sound-feedback-slider">
            <default>'Enabled'</default>
            <summary>change sound feedback slider status</summary>
            <description>声音-输入-反馈音量控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="sound-noise-reduce">
            <default>'Enabled'</default>
            <summary>change sound noise reduce status</summary>
            <description>声音-输入-噪音抑制控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="sound-effect-page">
            <default>'Enabled'</default>
            <summary>change sound effects status</summary>
            <description>声音-系统音效相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="bluetooth-transfile">
            <default>'Enabled'</default>
            <summary>change bluetooth transfile status</summary>
            <description>"发送文件"按钮的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="bluetooth-switch">
            <default>'Enabled'</default>
            <summary>change bluetooth powerswitch status</summary>
            <description>"蓝牙开关"的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="power-plans-label">
            <default>'Enabled'</default>
            <summary>change power plans label status</summary>
            <description>电源管理-通用-性能模式控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="power-autointo-saveenergy">
            <default>'Enabled'</default>
            <summary>change power autointo saveenergy status</summary>
            <description>电源管理-通用-低电量时自动开启控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="power-autointo-saveenergy-battery">
            <default>'Enabled'</default>
            <summary>change power autointo saveenergy battery status</summary>
            <description>电源管理-通用-使用电池时自动开启控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="power-lower-brightness">
            <default>'Enabled'</default>
            <summary>change power lower brightness status</summary>
            <description>电源管理-通用-自动降低亮度控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="power-showtime-tofull">
            <default>'Enabled'</default>
            <summary>change power showtimetofull status</summary>
            <description>电源管理-通用-显示剩余时间及剩余充电时间控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="power-showtime-tofulltips">
            <default>'Enabled'</default>
            <summary>change power showtimetofulltips status</summary>
            <description>电源管理-通用-最大容量控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="power-lid-present">
            <default>'Enabled'</default>
            <summary>change power lid present status</summary>
            <description>电源管理-使用电源-笔记本盒盖时控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="power-press-powerbtn">
            <default>'Enabled'</default>
            <summary>change power press powerbtn status</summary>
            <description>电源管理-使用电源-按电源按钮时控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="power-monitor-configure">
            <default>'Enabled'</default>
            <summary>change power monitor configure status</summary>
            <description>电源管理-使用电源-关闭显示器控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="power-auto-lockscreen">
            <default>'Enabled'</default>
            <summary>change power auto lockscreen status</summary>
            <description>电源管理-使用电源-自动锁屏控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="keyboard-shortcut">
            <default>'Enabled'</default>
            <summary>change keyboard shortcut status</summary>
            <description>键盘和语言-快捷键-列表控件的启用、显示</description>
        </key>
        <key name="keyboard-general" type="b">
            <default>true</default>
            <summary />
            <description>键盘和语言-通用的显示</description>
        </key>
        <key name="keyboard-layout" type="b">
            <default>true</default>
            <summary />
            <description>键盘和语言-键盘布局的显示</description>
        </key>
        <key name="keyboard-language" type="b">
            <default>true</default>
            <summary />
            <description>键盘和语言-系统语言的显示</description>
        </key>
        <key name="keyboard-shortcuts" type="b">
            <default>true</default>
            <summary />
            <description>键盘和语言-快捷键的显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="keyboard-general-numlock-enable">
            <default>'Enabled'</default>
            <summary>keyboard general numlock item is visible</summary>
        <description>键盘和语言-通用-启用数字键盘控件的启用、显示</description></key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="keyboard-general-capslock-enable">
            <default>'Enabled'</default>
            <summary>keyboard general capslock item is visible</summary>
        <description>键盘和语言-通用-大写锁定控件的启用、显示</description></key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="mouse-left-hand">
            <default>'Enabled'</default>
            <summary>change mouse left hand status</summary>
            <description>鼠标-通用-左手模式控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="mouse-touchpad">
            <default>'Enabled'</default>
            <summary>change mouse touchpad status</summary>
            <description>鼠标-通用-输入时禁用触控板控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="mouse-speed-slider">
            <default>'Enabled'</default>
            <summary>change mouse speed slider status</summary>
            <description>鼠标-鼠标-指针速度控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="mouse-acceleration">
            <default>'Enabled'</default>
            <summary>change mouse acceleration status</summary>
            <description>鼠标-鼠标-鼠标加速的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="mouse-natural-scrolling">
            <default>'Enabled'</default>
            <summary>change mouse natural scrolling status</summary>
            <description>鼠标-鼠标-自然滚动的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="touchpad-touchpad">
            <default>'Enabled'</default>
            <summary>change touchpad touchpad status</summary>
            <description>鼠标-触控板-触控板控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="touchpad-speed-slider">
            <default>'Enabled'</default>
            <summary>change touchpad speed slider status</summary>
            <description>鼠标-触控板-指针速度控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="touchpad-tap-click">
            <default>'Enabled'</default>
            <summary>change touchpad tap to click status</summary>
            <description>鼠标-触控板-轻触以点击的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="touchpad-natural-scrolling">
            <default>'Enabled'</default>
            <summary>change touchpad natural scrolling status</summary>
            <description>鼠标-触控板-自然滚动的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="trackpoint-speed-slider">
            <default>'Enabled'</default>
            <summary>change trackPoint speed slider status</summary>
            <description>鼠标-指点杆-指针速度控件的启用、显示</description>
        </key>
        <key name="mouse-general" type="b">
            <default>true</default>
            <summary />
            <description>鼠标-通用的显示</description>
        </key>
        <key name="mouse-mouse" type="b">
            <default>true</default>
            <summary />
            <description>鼠标-鼠标的显示</description>
        </key>
        <key name="mouse-touch" type="b">
            <default>true</default>
            <summary />
            <description>鼠标-触控板的显示</description>
        </key>
        <key name="mouse-trackpoint" type="b">
            <default>true</default>
            <summary />
            <description>鼠标-指点杆的显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="system-suspend">
            <default>'Enabled'</default>
            <summary>change system suspend status</summary>
            <description>电源管理-使用电源、使用电池-电脑进入待机模式控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="perssonal-general-effects">
            <default>'Enabled'</default>
            <summary />
            <description>个性化-通用-窗口效果相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="perssonal-general-themes">
            <default>'Enabled'</default>
            <summary />
            <description>个性化-通用-主题控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="perssonal-font-size">
            <default>'Enabled'</default>
            <summary />
            <description>个性化-字体-字号控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="perssonal-font-standard">
            <default>'Enabled'</default>
            <summary />
            <description>个性化-字体-标准字体控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="perssonal-font-mono">
            <default>'Enabled'</default>
            <summary />
            <description>个性化-字体-等宽字体控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="account-user-fullnamebtn">
            <default>'Enabled'</default>
            <summary />
            <description>账户全名编辑按钮显示控制</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="account-user-modifypasswd">
            <default>'Enabled'</default>
            <summary />
            <description>"修改密码"按钮显示控制</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="account-user-deleteaccount">
            <default>'Enabled'</default>
            <summary />
            <description>删除账户按钮显示控制</description>
        </key>
        <key name="account-fullname-enable" type="b">
            <default>true</default>
            <summary />
            <description>账户模块用户名称是否显示全名</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="defapp-applist-addbtn">
            <default>'Enabled'</default>
            <summary />
            <description>为程序添加默认程序"+"按钮的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="defapp-applist-defapp">
            <default>'Enabled'</default>
            <summary />
            <description>对应功能的默认程序的启用、显示</description>
        </key>
        <key name="defapp-webpage" type="b">
            <default>true</default>
            <summary />
            <description>默认程序“网页”的显示</description>
        </key>
        <key name="defapp-mail" type="b">
            <default>true</default>
            <summary />
            <description>默认程序“邮箱”的显示</description>
        </key>
        <key name="defapp-text" type="b">
            <default>true</default>
            <summary />
            <description>默认程序“文本”的显示</description>
        </key>
        <key name="defapp-music" type="b">
            <default>true</default>
            <summary />
            <description>默认程序“音乐”的显示</description>
        </key>
        <key name="defapp-video" type="b">
            <default>true</default>
            <summary />
            <description>默认程序“视频”的显示</description>
        </key>
        <key name="defapp-picture" type="b">
            <default>true</default>
            <summary />
            <description>默认程序“图片”的显示</description>
        </key>
        <key name="defapp-terminal" type="b">
            <default>true</default>
            <summary />
            <description>默认程序“终端”的显示</description>
        </key>
        <key name="personalization-general" type="b">
            <default>true</default>
            <summary />
            <description>个性化-通用的显示</description>
        </key>
        <key name="personalization-icon-theme" type="b">
            <default>true</default>
            <summary />
            <description>个性化-图标主题的显示</description>
        </key>
        <key name="personalization-font" type="b">
            <default>true</default>
            <summary />
            <description>个性化-字体的显示</description>
        </key>
        <key name="personalization-cursor-theme" type="b">
            <default>true</default>
            <summary />
            <description>个性化-光标主题的显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="datetime-datesetting-autosync">
            <default>'Enabled'</default>
            <summary />
            <description>"自动同步配置"控件启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="datetime-zonelist-addtimezone">
            <default>'Enabled'</default>
            <summary />
            <description>区域列表添加时区"+"按钮的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="datetime-datesetting-confirmbtn">
            <default>'Enabled'</default>
            <summary />
            <description>手动配置时间"确定"按钮的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="datetime-fromatsetting">
            <default>'Enabled'</default>
            <summary />
            <description>"时间格式设置"里面所有选项的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="systeminfo-nativeinfo-authorized">
            <default>'Enabled'</default>
            <summary />
            <description>系统信息-关于本机-版本授权相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="nativeinfo-authorize-button">
            <default>'Enabled'</default>
            <summary />
            <description>default is Enabled</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="systeminfo-nativeinfo-kernel">
            <default>'Enabled'</default>
            <summary />
            <description>系统信息-关于本机-内核版本控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="systeminfo-nativeinfo-processor">
            <default>'Enabled'</default>
            <summary />
            <description>系统信息-关于本机-处理器控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="systeminfo-nativeinfo-memory">
            <default>'Enabled'</default>
            <summary />
            <description>系统信息-关于本机-内存控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="commoninfo-boot-bootlist">
            <default>'Enabled'</default>
            <summary />
            <description>"启动列表"控件启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="commoninfo-boot-bootdelay">
            <default>'Enabled'</default>
            <summary />
            <description>"启动延时"控件启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="commoninfo-boot-theme">
            <default>'Enabled'</default>
            <summary />
            <description>"启动主题"控件启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="update-auto-check">
            <default>'Enabled'</default>
            <summary />
            <description>废弃(待确认)</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="update-auto-downlaod">
            <default>'Enabled'</default>
            <summary />
            <description>废弃(待确认)</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="update-update-notify">
            <default>'Enabled'</default>
            <summary />
            <description>废弃(待确认)</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="update-clean-cache">
            <default>'Enabled'</default>
            <summary />
            <description>废弃(待确认)</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="update-doc-store">
            <default>'Enabled'</default>
            <summary />
            <description>废弃(待确认)</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="update-system-update">
            <default>'Enabled'</default>
            <summary />
            <description>废弃(待确认)</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="update-app-update">
            <default>'Enabled'</default>
            <summary />
            <description>废弃(待确认)</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="update-secure-update">
            <default>'Enabled'</default>
            <summary />
            <description>废弃(待确认)</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wired-switch">
            <default>'Enabled'</default>
            <summary />
            <description>网络-有线网络-三级菜单相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="add-connection">
            <default>'Enabled'</default>
            <summary />
            <description>添加网络连接按钮显示控制</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="remove-connection">
            <default>'Enabled'</default>
            <summary />
            <description>网络-连接详情中删除按钮控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wired-edit-connection-name">
            <default>'Enabled'</default>
            <summary />
            <description>网络-有线网络-详情页面-名称控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wired-auto-connect">
            <default>'Enabled'</default>
            <summary />
            <description>网络-有线网络-详情页面-自动连接控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wired-security">
            <default>'Enabled'</default>
            <summary />
            <description>网络-有线网络-详情页面-安全相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wired-ipv4">
            <default>'Enabled'</default>
            <summary />
            <description>网络-有线网络-详情页面-IPv4相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wired-ipv6">
            <default>'Enabled'</default>
            <summary />
            <description>网络-有线网络-详情页面-IPv6相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wired-ether-net">
            <default>'Enabled'</default>
            <summary />
            <description>网络-有线网络-详情页面-以太网相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wireless">
            <default>'Enabled'</default>
            <summary />
            <description>网络-无线网络的显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wireless-auto-connect">
            <default>'Enabled'</default>
            <summary />
            <description>网络-无线网络-详情页面-自动连接控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wireless-security">
            <default>'Enabled'</default>
            <summary />
            <description>网络-无线网络-详情页面-安全相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wireless-ipv4">
            <default>'Enabled'</default>
            <summary />
            <description>网络-无线网络-详情页面-IPv4相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wireless-ipv6">
            <default>'Enabled'</default>
            <summary />
            <description>网络-无线网络-详情页面-IPv6相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wireless-wlan">
            <default>'Enabled'</default>
            <summary />
            <description>网络-无线网络-详情页面-WLAN相关控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="create-pppoe">
            <default>'Enabled'</default>
            <summary />
            <description>创建DSL按钮的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="create-vpn">
            <default>'Enabled'</default>
            <summary />
            <description>创建VPN按钮的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="import-vpn">
            <default>'Enabled'</default>
            <summary />
            <description>"导入VPN"控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="create-hotspot">
            <default>'Enabled'</default>
            <summary />
            <description>创建热点按钮的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="edition">
            <default>'Enabled'</default>
            <summary />
            <description>"版本"控件的启用、显示</description>
        </key>
        <key name="boot-menu" type="b">
            <default>true</default>
            <summary />
            <description>"启动菜单"显示</description>
        </key>
        <key name="commoninfo-boot-wallpaper-config" type="b">
            <default>true</default>
            <summary />
            <description>"启动菜单"grub背景图片可否编辑</description>
        </key>
        <key name="developer-mode" type="b">
            <default>true</default>
            <summary />
            <description>“开发者模式”的显示</description>
        </key>
        <key name="user-experience-program" type="b">
            <default>true</default>
            <summary />
            <description>通用-用户体验计划的显示</description>
        </key>
        <key name="timezone-list" type="b">
            <default>true</default>
            <summary />
            <description>时间日期-时区列表的显示</description>
        </key>
        <key name="time-settings" type="b">
            <default>true</default>
            <summary />
            <description>时间日期-时间设置的显示</description>
        </key>
        <key name="time-format" type="b">
            <default>true</default>
            <summary />
            <description>时间日期-格式设置的显示</description>
        </key>
        <key name="network-dsl" type="b">
            <default>true</default>
            <summary />
            <description>网络-DSL的显示</description>
        </key>
        <key name="network-vpn" type="b">
            <default>true</default>
            <summary />
            <description>网络-VPN的显示</description>
        </key>
        <key name="system-proxy" type="b">
            <default>true</default>
            <summary />
            <description>网络-系统代理的显示</description>
        </key>
        <key name="application-proxy" type="b">
            <default>true</default>
            <summary />
            <description>"应用代理"显示控制</description>
        </key>
        <key name="network-details" type="b">
            <default>true</default>
            <summary />
            <description>网络-网络详情的显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="hotspot-switch">
            <default>'Enabled'</default>
            <summary />
            <description>“热点”开关的启用、显示</description>
        </key>
        <key name="personal-hotspot" type="b">
            <default>true</default>
            <summary />
            <description>网络-个人热点的显示</description>
        </key>
        <key name="network-wired" type="b">
            <default>true</default>
            <summary />
            <description>网络-有线网络的显示</description>
        </key>
        <key name="network-wireless" type="b">
            <default>true</default>
            <summary />
            <description>网络-无线网络的显示</description>
        </key>
        
        <key name="general" type="b">
            <default>true</default>
            <summary />
            <description>电源-"通用"的显示</description>
        </key>
        <key name="plugged-in" type="b">
            <default>true</default>
            <summary />
            <description>电源管理-使用电源的显示</description>
        </key>
        <key name="on-battery" type="b">
            <default>true</default>
            <summary />
            <description>电源管理-使用电池的显示</description>
        </key>
        
        <key name="authentication-finger" type="b">
            <default>true</default>
            <summary />
            <description>生物识别-指纹显示控制</description>
        </key>
        <key name="authentication-face" type="b">
            <default>true</default>
            <summary />
            <description>生物识别-人脸显示控制</description>
        </key>
        <key name="authentication-iris" type="b">
            <default>true</default>
            <summary />
            <description>生物识别-虹膜显示控制</description>
        </key>
        
        <key name="sound-output" type="b">
            <default>true</default>
            <summary />
            <description>声音-输出的显示</description>
        </key>
        <key name="sound-input" type="b">
            <default>true</default>
            <summary />
            <description>声音-输入的显示</description>
        </key>
        <key name="sound-effects" type="b">
            <default>true</default>
            <summary />
            <description>声音-系统音效的显示</description>
        </key>
        <key name="device-manage" type="b">
            <default>true</default>
            <summary />
            <description>声音-“设备管理”菜单的显示</description>
        </key>
        <key name="about-this-pc" type="b">
            <default>true</default>
            <summary />
            <description>关于本机显示控制</description>
        </key>
        <key name="edition-license" type="b">
            <default>true</default>
            <summary />
            <description>"版本协议"的显示</description>
        </key>
        <key name="end-user-license-agreement" type="b">
            <default>true</default>
            <summary />
            <description>"最终用户许可协议"的显示</description>
        </key>
        <key name="privacy-policy" type="b">
            <default>true</default>
            <summary />
            <description>系统信息-隐私政策的显示</description>
        </key>
        <key name="system-notification" type="b">
            <default>true</default>
            <summary />
            <description>通知-系统通知的显示</description>
        </key>
        <key name="app-notifications" type="b">
            <default>true</default>
            <summary />
            <description>"应用通知"显示控制</description>
        </key>
        <key name="resolution-config" type="s">
            <default>'1024*768'</default>
            <summary />
            <description>显示-分辨率中列出的最小分辨率阈值</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="terminal-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-终端控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="terminal-quake-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-终端雷神模式控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="screenshot-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-截图控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="screenshot-delayed-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-延时截图控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="screenshot-fullscreen-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-全屏截图控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="screenshot-window-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-窗口截图控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="screenshot-scroll-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-滚动截图控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="screenshot-ocr-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-图文识别控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="deepin-screen-recorder-config">
            <default>'Enabled'</default>
            <description>"录屏"快捷键设置的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="switch-group-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-切换同类型窗口控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="switch-group-backward-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-反向切换同类型窗口控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="preview-workspace-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-显示工作区控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="expose-windows-config">
            <default>'Enabled'</default>
            <description>"显示当前工作区的窗口"快捷键设置的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="expose-all-windows-config">
            <default>'Enabled'</default>
            <description>"显示所有工作区的窗口"快捷键设置的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="launcher-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-启动器控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="switch-applications-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-切换窗口控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="switch-applications-backward-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-反向切换窗口控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="show-desktop-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-显示桌面控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="file-manager-config">
            <default>'Enabled'</default>
            <description>"文件管理器"快捷键设置的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="lock-screen-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-锁屏控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="logout-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-关机界面控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="wm-switcher-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-切换窗口特效控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="system-monitor-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-系统监视器控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="clipboard-config">
            <default>'Enabled'</default>
            <description>"剪切板"快捷键设置的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="global-search-config">
            <default>'Enabled'</default>
            <description>"全局搜索"快捷键设置的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="maximize-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-最大化窗口控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="unmaximize-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-恢复窗口控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="minimize-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-最小化窗口控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="begin-move-config">
            <default>'Enabled'</default>
            <description>"移动窗口"快捷键设置的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="begin-resize-config">
            <default>'Enabled'</default>
            <description>"改变窗口大小"快捷键设置的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="close-config">
            <default>'Enabled'</default>
            <description>"关闭窗口"快捷键设置的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="switch-to-workspace-left-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-切换到左边工作区控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="switch-to-workspace-right-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-切换到右边工作区控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="move-to-workspace-left-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-移动到左边工作区控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="move-to-workspace-right-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-移动到右边工作区控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="ai-assistant-config">
            <default>'Enabled'</default>
            <description>桌面智能助手快捷键设置显示控制</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="text-to-speech-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-语音朗读控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="speech-to-text-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-语音听写控件的启用、显示</description>
        </key>
        <key enum="com.deepin.dde.control-center.StatusMode" name="translation-config">
            <default>'Enabled'</default>
            <description>键盘和语言-快捷键-文本翻译控件的启用、显示</description>
        </key>
        <key name="wireless-scan-interval" type="i">
            <default>10</default>
            <summary>wireless scan interval</summary>
        <description>废弃</description></key>
    </schema>
</schemalist>
