<?xml version="1.0" encoding="UTF-8"?>
<schemalist>
  <enum id="com.deepin.dde.dock.module.StatusMode">
        <value value="0" nick="Enabled" />
        <value value="1" nick="Disabled" />
        <value value="2" nick="Hiden" />
    </enum>
  <schema path="/com/deepin/dde/dock/module/app/" id="com.deepin.dde.dock.module.app" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
            Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
            Control Module Enable
      </description>
    </key>
	<key type="b" name="removeable">
      <default>true</default>
      <summary>App Removeable</summary>
      <description>
          Control App Removeable
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/disableplugins/" id="com.deepin.dde.dock.disableplugins" gettext-domain="DDE">
     <key name='disable-plugins-list' type='as'>
        <default>[]</default>
        <summary>disable loading plugins</summary>
        <description>dock disable loading plugins</description>
     </key>
  </schema>
  <schema path="/com/deepin/dde/dock/distancemultiple/" id="com.deepin.dde.dock.distancemultiple" gettext-domain="DDE">
     <key name='distance-multiple' type='d'>
        <default>1.5</default>
        <range min="1.0" max="5.0"/>
        <summary>
          the distance multiple factor between the dragged resident app and the dock
        </summary>
        <description>
          The distance away from the dock of the drag resident application divided by the width or height of the dock (the smaller one)
        </description>
     </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/activeapp/" id="com.deepin.dde.dock.module.activeapp" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/dockapp/" id="com.deepin.dde.dock.module.dockapp" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/launcher/" id="com.deepin.dde.dock.module.launcher" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/datetime/" id="com.deepin.dde.dock.module.datetime" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="menu-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          Control Menu Enable
      </description>
    </key>
    <key type="b" name="visible">
      <default>true</default>
      <summary>Module visible</summary>
      <description>
          Control Menu pluginsettings visible
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/network/" id="com.deepin.dde.dock.module.network" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="menu-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          Control Menu Enable
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/keyboard/" id="com.deepin.dde.dock.module.keyboard" gettext-domain="DDE">
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="item-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          disable item
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/onboard/" id="com.deepin.dde.dock.module.onboard" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="menu-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          Control Menu Enable
      </description>
    </key>
    <key type="b" name="visible">
      <default>true</default>
      <summary>Module visible</summary>
      <description>
          Control Menu pluginsettings visible
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/bluetooth/" id="com.deepin.dde.dock.module.bluetooth" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="menu-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          Control Menu Enable
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/power/" id="com.deepin.dde.dock.module.power" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="menu-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          Control Menu Enable
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/shutdown/" id="com.deepin.dde.dock.module.shutdown" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="menu-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          Control Menu Enable
      </description>
    </key>
    <key type="b" name="visible">
      <default>true</default>
      <summary>Module visible</summary>
      <description>
          Control Menu pluginsettings visible
      </description>
    </key>
    <key name="show-suspend" type="b">
        <default>true</default>
        <summary>The suspend options show or not</summary>
    </key>
    <key name="show-hibernate" type="b">
        <default>true</default>
        <summary>The hibernate options show or not</summary>
    </key>
    <key name="show-shutdown" type="b">
        <default>true</default>
        <summary>The shutdown options show or not</summary>
    </key>
    <key name="show-lock" type="b">
        <default>true</default>
        <summary>The lock options show or not</summary>
    </key>
    <key name="show-reboot" type="b">
        <default>true</default>
        <summary>The reboot options show or not</summary>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/sound/" id="com.deepin.dde.dock.module.sound" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="menu-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          Control Menu Enable
      </description>
    </key>
    <key enum="com.deepin.dde.dock.module.StatusMode" name="sound-output-slider">
      <default>'Enabled'</default>
      <summary>change sound output slider status</summary>
      <description>default is Enabled</description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/trash/" id="com.deepin.dde.dock.module.trash" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="menu-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          Control Menu Enable
      </description>
    </key>
    <key type="b" name="visible">
      <default>true</default>
      <summary>Module visible</summary>
      <description>
          Control Menu pluginsettings visible
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/systemtray/" id="com.deepin.dde.dock.module.systemtray" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/airplane-mode/" id="com.deepin.dde.dock.module.airplane-mode" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>false</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/module/tray/" id="com.deepin.dde.dock.module.tray" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
  </schema>
 <schema path="/com/deepin/dde/dock/module/menu/" id="com.deepin.dde.dock.module.menu" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
      </key>
    <key type="b" name="mode-visible">
      <default>true</default>
      <summary>Module Visible</summary>
      <description>
          Control Module Visible
      </description>
    </key>
    <key type="b" name="location-visible">
      <default>true</default>
      <summary>Module Visible</summary>
      <description>
          Control Module Visible
      </description>
    </key>
    <key type="b" name="status-visible">
      <default>true</default>
      <summary>Module Visible</summary>
      <description>
          Control Module Visible
      </description>
    </key>
    <key type="b" name="setting-visible">
      <default>true</default>
      <summary>Module Visible</summary>
      <description>
          Control Setting Menu Visible
      </description>
    </key>
    <!-- start 以下配置不再起作用，对应的菜单已经被删除，为了防止定制化场景调用出错，暂时保留-->
    <key type="b" name="hide-visible">
      <default>true</default>
      <summary>Module Visible</summary>
      <description>
          Control Module Visible
      </description>
    </key>
    <key type="b" name="multiscreen-visible">
      <default>true</default>
      <summary>Multiscreen Visible</summary>
      <description>
          Control Multiscreen Visible
      </description>
    </key>
    <!-- end -->
  </schema>
 <schema path="/com/deepin/dde/dock/module/AiAssistant/" id="com.deepin.dde.dock.module.AiAssistant" gettext-domain="DDE">
    <key type="b" name="control">
      <default>false</default>
      <summary>Blocking event</summary>
      <description>
          Blocking mouse events
      </description>
    </key>
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="menu-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          Control Menu Enable
      </description>
    </key>
    <key type="b" name="visible">
      <default>true</default>
      <summary>Module visible</summary>
      <description>
          Control Menu pluginsettings visible
      </description>
    </key>
  </schema>
 <schema path="/com/deepin/dde/dock/module/multitasking/" id="com.deepin.dde.dock.module.multitasking" gettext-domain="DDE">
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="menu-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          Control Menu Enable
      </description>
    </key>
    <key type="b" name="visible">
      <default>true</default>
      <summary>Module visible</summary>
      <description>
          Control Menu pluginsettings visible
      </description>
    </key>
  </schema>
 <schema path="/com/deepin/dde/dock/module/show-desktop/" id="com.deepin.dde.dock.module.show-desktop" gettext-domain="DDE">
    <key type="b" name="enable">
      <default>true</default>
      <summary>Module Enable</summary>
      <description>
          Control Module Enable
      </description>
    </key>
    <key type="b" name="menu-enable">
      <default>true</default>
      <summary>Menu Enable</summary>
      <description>
          Control Menu Enable
      </description>
    </key>
    <key type="b" name="visible">
      <default>true</default>
      <summary>Module visible</summary>
      <description>
          Control Menu pluginsettings visible
      </description>
    </key>
  </schema>
  <schema path="/com/deepin/dde/dock/touch/" id="com.deepin.dde.dock.touch" gettext-domain="DDE">
    <key type="i" name="resize-height">
      <default>7</default>
      <summary>Controling the drag height of touch screen</summary>
      <description>
          Controling the drag height of touch screen
      </description>
    </key>
  </schema>
 <schema path="/com/deepin/dde/dock/mainwindow/" id="com.deepin.dde.dock.mainwindow" gettext-domain="DDE">
    <key type="u" name="monitors-switch-time">
      <default>2000</default>
      <summary>Unit: ms</summary>
      <description>Control duration of dock switch in monitors</description>
    </key>
    <key type="b" name="only-show-primary">
      <default>true</default>
      <summary></summary>
      <description>Determine to show dock only in primary monitor</description>
    </key>
  </schema>
</schemalist>
